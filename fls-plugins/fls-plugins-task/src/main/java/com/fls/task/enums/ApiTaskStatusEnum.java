package com.fls.task.enums;

import lombok.Getter;

@Getter
public enum ApiTaskStatusEnum {
    /**
     * 待调度
     */
    PREPARE("0", "待调度"),

    /**
     * 进行中
     */
    PROGRESS("1", "进行中"),

    /**
     * 执行成功
     */
    SUCCESS("2", "执行成功"),

    /**
     * 执行失败
     */
    FAIL("3", "执行失败"),
    /**
     * 回调成功
     */
    CALL_BACK_SUCCESS("4", "回调成功"),

    /**
     * 回调失败
     */
    CALL_BACK_FAILED("5", "回调失败");

    private final String code;

    private final String name;

    ApiTaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
