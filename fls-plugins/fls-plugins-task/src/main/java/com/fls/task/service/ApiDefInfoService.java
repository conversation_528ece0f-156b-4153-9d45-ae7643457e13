package com.fls.task.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fls.task.entity.ApiDefInfoEntity;

/**
 * <p>
 * 接口定义表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@DS("task")
public interface ApiDefInfoService extends IService<ApiDefInfoEntity> {

    /**
     * 通过code获取接口定义信息
     *
     * @param apiCode 接口编码
     * @return 接口定义信息
     */
    ApiDefInfoEntity getByApiCode(String apiCode);
}
