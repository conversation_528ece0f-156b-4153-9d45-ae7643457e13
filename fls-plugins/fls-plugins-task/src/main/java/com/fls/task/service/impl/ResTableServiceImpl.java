package com.fls.task.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;

import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.task.constant.ApiConstant;
import com.fls.task.pojo.model.ResTableInfo;
import com.fls.task.service.IResTableService;

import lombok.RequiredArgsConstructor;

/**
 * ResTableServiceImpl
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
@RequiredArgsConstructor
public class ResTableServiceImpl implements IResTableService {

    private final ApiClientFacade apiClientFacade;

    @Override
    public ResTableInfo queryResTableInfo(String idRes) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("id", idRes).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_RESOURCE_DETAIL, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        return BeanUtil.toBean(response.getData(), ResTableInfo.class);
    }
}
