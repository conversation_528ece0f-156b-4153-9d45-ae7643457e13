package com.fls.task.core.handler;

import org.springframework.stereotype.Component;

import com.fls.task.core.model.ApiResponse;
import com.fls.task.pojo.req.ApiTaskReq;

/**
 * 默认接口任务处理器
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Component
public class DefaultApiTaskHandler implements IApiTaskHandler {

    @Override
    public void handleCallback(ApiTaskReq apiTaskReq, ApiResponse response) {

    }
}
