package com.fls.task.core.manager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.springframework.stereotype.Component;

import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.lisenter.TaskStateListener;
import com.fls.task.core.thread.TaskThread;

import lombok.extern.slf4j.Slf4j;

/**
 * 定时调度任务管理器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component
public class ScheduleTaskManager {

    // 定时调度任务线程仓库
    private final ConcurrentMap<String, TaskThread> taskThreadRepository = new ConcurrentHashMap<>();

    /**
     * 注册定时调度任务线程
     */
    public TaskThread registTaskThread(String taskScheduleId, ITaskHandler handler, TaskStateListener listener, String removeOldReason) {
        TaskThread newTaskThread = new TaskThread(taskScheduleId, handler, listener);
        newTaskThread.start();

        log.info(">>>>>>>>>>> task-executor register TaskThread success, taskScheduleId:{}, handler:{}", taskScheduleId, handler);

        TaskThread oldTaskThread = taskThreadRepository.put(taskScheduleId, newTaskThread);
        if (oldTaskThread != null) {
            oldTaskThread.toStop(removeOldReason);
            oldTaskThread.interrupt();
        }

        return newTaskThread;
    }

    /**
     * 移除定时调度任务线程
     */
    public TaskThread removeTaskThread(String taskScheduleId, String removeOldReason) {
        TaskThread oldTaskThread = taskThreadRepository.remove(taskScheduleId);
        if (oldTaskThread != null) {
            oldTaskThread.toStop(removeOldReason);
            oldTaskThread.interrupt();
            log.info("ScheduleTask removed successfully, taskScheduleId:{}", taskScheduleId);
            return oldTaskThread;
        }
        return null;
    }

    /**
     * 加载定时调度任务线程
     */
    public TaskThread loadTaskThread(String taskScheduleId) {
        return taskThreadRepository.get(taskScheduleId);
    }

    /**
     * 获取所有任务线程
     */
    public Map<String, TaskThread> getAllTaskThreads() {
        return new ConcurrentHashMap<>(taskThreadRepository);
    }

    /**
     * 清理所有任务线程
     */
    public void destroyAllTaskThreads() {
        // 清除task repository
        if (!taskThreadRepository.isEmpty()) {
            // wait for job thread push result to callback queue
            for (Map.Entry<String, TaskThread> item : taskThreadRepository.entrySet()) {
                TaskThread oldTaskThread = removeTaskThread(item.getKey(), "task executor container destroy and kill the task.");
                // wait for job thread push result to callback queue
                if (oldTaskThread != null) {
                    try {
                        oldTaskThread.join();
                    } catch (InterruptedException e) {
                        log.error(">>>>>>>>>>> task-executor, TaskThread destroy(join) error, schedId:{}", item.getKey(), e);
                    }
                }
            }
        }
        taskThreadRepository.clear();
        log.info("All ScheduleTasks destroyed successfully");
    }
}