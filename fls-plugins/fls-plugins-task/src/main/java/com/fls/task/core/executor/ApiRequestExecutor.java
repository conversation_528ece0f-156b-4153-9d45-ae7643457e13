package com.fls.task.core.executor;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.task.core.model.ApiRequestContext;
import com.fls.task.core.model.ApiResponse;
import com.fls.task.enums.ApiTaskStatusEnum;
import com.fls.task.enums.ApiTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * API请求执行器，负责处理HTTP请求和服务总线请求
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiRequestExecutor {

    private final ApiClientFacade apiClientFacade;

    /**
     * 执行API请求
     *
     * @param context API请求上下文
     * @return API响应结果
     */
    public ApiResponse execute(ApiRequestContext context) throws Exception {
        log.info("开始执行API请求，请求信息：{}", JSON.toJSONString(context));
        ApiResponse response;
        if (ApiTypeEnum.ORIGIN.getType().equals(context.getApiType())) {
            response = executeHttpRequest(context);
        }
        else {
            response = executeServiceBusRequest(context);
        }
        response.setIdTask(context.getTaskId());
        response.setTaskStatus(response.isSuccess() ? ApiTaskStatusEnum.SUCCESS.getCode() : ApiTaskStatusEnum.FAIL.getCode());
        return response;
    }

    private ApiResponse executeServiceBusRequest(ApiRequestContext context) {
        String result = apiClientFacade.invoke(context.getMethod(), context.getPath(), JSONUtil.parseObj(context.getRequestData()));
        ApiResponse apiResponse = parseResponse(result);
        if (ApiTypeEnum.CHAIN.getType().equals(context.getApiType())) {
            //尝试写入sccc task id
            String rawResponse = apiResponse.getRawResponse();
            JSONObject jsonResponse = JSON.parseObject(rawResponse);
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data != null) {
                String idScccTask = data.getString("idTask");
                context.setIdScccTask(idScccTask);
            }
        }
        return apiResponse;
    }

    private ApiResponse executeHttpRequest(ApiRequestContext context) {
        HttpRequest httpRequest = HttpRequest.post(context.getUrl()).body(context.getRequestData());

        if (context.getTimeout() != null) {
            httpRequest.timeout(context.getTimeout());
        }

        log.info("发起HTTP请求：URL={}, Body={}", context.getHost(), context.getRequestData());

        HttpResponse response = httpRequest.execute();
        log.info("收到HTTP响应：{}", response.body());

        return parseResponse(response.body());
    }

    private ApiResponse parseResponse(String responseBody) {
        try {
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            ApiResponse.ApiResponseBuilder builder = ApiResponse.builder();
            builder.code(jsonResponse.getString("code"))
                    .message(jsonResponse.getString("message") != null ? jsonResponse.getString("message") : jsonResponse.getString("msg"))
                    .rawResponse(responseBody);
            return builder.build();
        }
        catch (Exception e) {
            log.error("解析响应失败", e);
            throw new RuntimeException("解析响应失败", e);
        }
    }
}