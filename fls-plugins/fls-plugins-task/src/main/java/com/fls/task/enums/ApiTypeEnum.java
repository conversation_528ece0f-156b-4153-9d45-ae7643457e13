package com.fls.task.enums;

import lombok.Getter;

/**
 * 接口请求类型枚举
 */
@Getter
public enum ApiTypeEnum {
    /**
     * 原生http请求
     */
    SERVICE("0", "总线"),

    /**
     * 总线服务请求
     */
    ORIGIN("1", "原生"),

    /**
     * 链式任务请求
     */
    CHAIN("2", "链式");

    private final String type;

    private final String name;

    ApiTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }
}
