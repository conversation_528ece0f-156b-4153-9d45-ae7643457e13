package com.fls.task.pojo.model;

import lombok.Data;

/**
 * ApiRequestInfo
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
public class ApiRequestInfo {

    /**
     * 接口编码
     */
    private String apiCode;

    /**
     * 接口类型
     */
    private String apiType;

    /**
     * 请求url
     */
    private String reqUrl;

    /**
     * 请求方法
     */
    private String reqMethod;

    /**
     * 请求内容
     */
    private String reqBody;

    /**
     * 来源单据id
     */
    private String idSource;

    /**
     * 来源单据号
     */
    private String codeSource;

    /**
     * 来源单据资源id
     */
    private String idSourceRes;

    /**
     * 请求参数
     */
    private String requestData;

    /**
     * 超时时间，单位：s
     */
    private Integer timeout;
}
