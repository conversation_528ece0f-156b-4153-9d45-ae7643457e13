package com.fls.task.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.task.entity.TaskScheduleInfoEntity;
import com.fls.task.mapper.TaskScheduleInfoMapper;
import com.fls.task.service.TaskScheduleInfoService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 任务调度信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@Slf4j
public class TaskScheduleInfoServiceImpl extends ServiceImpl<TaskScheduleInfoMapper, TaskScheduleInfoEntity> implements TaskScheduleInfoService {

}
