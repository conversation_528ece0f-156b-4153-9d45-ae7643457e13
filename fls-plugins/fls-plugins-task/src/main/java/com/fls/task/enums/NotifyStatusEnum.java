package com.fls.task.enums;

import lombok.Getter;

/**
 * 回调状态枚举
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Getter
public enum NotifyStatusEnum {
    /**
     * 待调度
     */
    FAILED("0", "回调失败"),

    /**
     * 进行中
     */
    SUCCESS("1", "回调成功");

    private final String code;

    private final String name;

    NotifyStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
