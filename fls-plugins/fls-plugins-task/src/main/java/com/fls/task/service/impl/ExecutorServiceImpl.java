package com.fls.task.service.impl;

import org.springframework.stereotype.Service;

import com.fls.task.core.executor.TaskExecutor;
import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.thread.TaskThread;
import com.fls.task.enums.TriggerTypeEnum;
import com.fls.task.pojo.model.OnceTaskInfo;
import com.fls.task.pojo.param.TriggerParam;
import com.fls.task.service.ApiTaskService;
import com.fls.task.service.ExecutorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ExecutorServiceImpl
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExecutorServiceImpl implements ExecutorService {

    private final TaskExecutor taskExecutor;

    private final ApiTaskService apiTaskService;

    @Override
    public boolean run(TriggerParam triggerParam) {
        String schedulerInfoId = triggerParam.getSchedulerInfoId();
        String idApiTask = triggerParam.getIdApiTask();
        String triggerType = triggerParam.getTriggerType();
        String logId = triggerParam.getLogId();

        // 通过idApiTask构建ITaskHandler
        ITaskHandler taskHandler = apiTaskService.buildApiTask(idApiTask);
        if (taskHandler == null) {
            log.warn("Failed to build task handler for idApiTask: {}", idApiTask);
            return false;
        }
        // 根据触发类型选择执行方式
        if (TriggerTypeEnum.ONCE.name().equals(triggerType) || TriggerTypeEnum.MANUAL.name().equals(triggerType)) {
            // 一次性任务处理
            return handleOnceTask(logId, taskHandler, triggerParam);
        } else if (TriggerTypeEnum.CRON.name().equals(triggerType) || TriggerTypeEnum.INTERVAL.name().equals(triggerType)) {
            // 定时调度任务处理
            return handleScheduleTask(schedulerInfoId, taskHandler, triggerParam);
        } else {
            log.warn("Unsupported trigger type: {}", triggerType);
            return false;
        }
    }

    /**
     * 处理一次性任务
     */
    private boolean handleOnceTask(String idTask, ITaskHandler taskHandler, TriggerParam triggerParam) {
        // 构建一次性任务信息
        OnceTaskInfo onceTaskInfo = new OnceTaskInfo();
        onceTaskInfo.setIdTask(idTask);
        onceTaskInfo.setTaskHandler(taskHandler);
        onceTaskInfo.setTriggerParam(triggerParam);
        // 提交一次性任务
        return taskExecutor.submitOnceTask(idTask, onceTaskInfo);
    }

    /**
     * 处理定时调度任务
     */
    private boolean handleScheduleTask(String schedulerInfoId, ITaskHandler taskHandler, TriggerParam triggerParam) {
        String removeOldReason = null;
        // task executor load thread
        TaskThread taskThread = taskExecutor.loadScheduleTaskThread(schedulerInfoId);
        ITaskHandler currentHandler = taskThread != null ? taskThread.getHandler() : null;

        // 比对thead和handler
        if (taskThread != null && currentHandler != taskHandler) {
            // change handler, need kill old thread
            removeOldReason = "task thread handler changed, try to discard old task handler";

            taskThread = null;
            currentHandler = null;
        }

        if (currentHandler == null) {
            currentHandler = taskHandler;
        }

        // 为空则register，不为空则按照阻塞策略进行填充
        if (taskThread == null) {
            taskThread = taskExecutor.registScheduleTaskThread(schedulerInfoId, currentHandler, removeOldReason);
        }

        // push data
        return taskThread != null && taskThread.pushTriggerQueue(triggerParam);
    }

    @Override
    public boolean kill(TriggerParam triggerParam) {
        String schedulerInfoId = triggerParam.getSchedulerInfoId();
        // kill handlerThread, and create new one
        TaskThread taskThread = taskExecutor.loadScheduleTaskThread(schedulerInfoId);
        if (taskThread == null) {
            log.warn("task thread already closed");
        }
        taskExecutor.removeScheduleTaskThread(schedulerInfoId, "scheduling center kill task.");
        return true;
    }
}
