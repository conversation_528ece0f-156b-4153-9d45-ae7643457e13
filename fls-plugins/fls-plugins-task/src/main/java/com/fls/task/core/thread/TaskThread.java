package com.fls.task.core.thread;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.FutureTask;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import cn.hutool.core.collection.CollectionUtil;

import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.lisenter.TaskStateListener;
import com.fls.task.pojo.param.TriggerParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 任务线程
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
public class TaskThread extends Thread {

    private final TaskStateListener stateListener;

    /**
     * 触发参数的队列
     */
    private final LinkedBlockingQueue<TriggerParam> triggerQueue;
    /**
     * 任务调度id
     */
    private final String taskSchedulerId;
    /**
     * 任务处理器
     */
    private final ITaskHandler handler;
    /**
     * 执行日志集合
     */
    private final Set<String> triggerLogIdSet;
    /**
     * 线程停止标识
     */
    private volatile boolean toStop = false;

    /**
     * 停止原因
     */
    private String stopReason;

    /**
     * 运行标识
     */
    private boolean running = false;

    /**
     * 空闲次数
     */
    private int idleTimes = 0;

    public TaskThread(String taskSchedulerId, ITaskHandler handler, TaskStateListener stateListener) {
        this.taskSchedulerId = taskSchedulerId;
        this.handler = handler;
        this.triggerQueue = new LinkedBlockingQueue<>();
        this.stateListener = stateListener;
        this.triggerLogIdSet = Collections.synchronizedSet(new HashSet<>());

        // assign job thread name
        this.setName("task-executor, TaskThread-" + taskSchedulerId + "-" + System.currentTimeMillis());
    }

    public ITaskHandler getHandler() {
        return handler;
    }

    /**
     * 推触发参数至队列
     * 
     * @param triggerParam 触发参数
     * @return 执行成功标识
     */
    public boolean pushTriggerQueue(TriggerParam triggerParam) {
        if (triggerLogIdSet.contains(triggerParam.getLogId())) {
            log.info(">>>>>>>>>>> repeat trigger task, logId:{}", triggerParam.getLogId());
            return false;
        }

        triggerLogIdSet.add(triggerParam.getLogId());
        triggerQueue.add(triggerParam);
        return true;
    }

    /**
     * 停止该任务线程
     * 
     * @param stopReason 停止原因
     */
    public void toStop(String stopReason) {
        this.toStop = true;
        this.stopReason = stopReason;
    }

    /**
     * 线程是否正常运行
     * 
     * @return 布尔结果
     */
    public boolean isRunningOrHasQueue() {
        return running || CollectionUtil.isNotEmpty(triggerQueue);
    }

    @Override
    public void run() {
        // init
        try {
            handler.init();
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }

        // execute
        while (!toStop) {
            running = false;
            idleTimes++;

            TriggerParam triggerParam = null;
            try {
                // to check toStop signal, we need cycle, so we cannot use queue.take(), instead of poll(timeout)
                triggerParam = triggerQueue.poll(3L, TimeUnit.SECONDS);
                if (triggerParam != null) {
                    running = true;
                    idleTimes = 0;
                    triggerLogIdSet.remove(triggerParam.getLogId());

                    if (triggerParam.getTimeout() > 0) {
                        // limit timeout
                        Thread futureThread = null;
                        try {
                            FutureTask<Boolean> futureTask = new FutureTask<>(() -> {
                                handler.execute();
                                return true;
                            });
                            futureThread = new Thread(futureTask);
                            futureThread.start();

                            Boolean tempResult = futureTask.get(triggerParam.getTimeout(), TimeUnit.SECONDS);
                        } catch (TimeoutException e) {
                            log.error(e.getMessage(), e);
                        } finally {
                            futureThread.interrupt();
                        }
                    } else {
                        // just execute
                        handler.execute();
                    }
                } else {
                    if (idleTimes > 30) {
                        if (triggerQueue.isEmpty()) {
                            stateListener.onTaskIdle(taskSchedulerId, "executor idle times over limit.");
                        }
                    }
                }
            } catch (Throwable e) {
                // handle result
                StringWriter stringWriter = new StringWriter();
                e.printStackTrace(new PrintWriter(stringWriter));
                String errorMsg = stringWriter.toString();
                log.error("----------- TaskThread Exception:{}----------- task execute end(error) -----------", errorMsg);
            }
        }

        if (toStop) {
            log.info(">>>>>>>>>>> TaskThread toStop, stopReason:{}", stopReason);
        }
        try {
            handler.destroy();
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }

        log.info(">>>>>>>>>>> task-executor TaskThread stoped, hashCode:{}", Thread.currentThread());
    }
}
