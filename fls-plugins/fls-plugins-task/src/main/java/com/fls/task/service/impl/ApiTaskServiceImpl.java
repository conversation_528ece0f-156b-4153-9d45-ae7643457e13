package com.fls.task.service.impl;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;

import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.framework.core.util.StringUtils;
import com.fls.framework.security.util.LoginHelper;
import com.fls.openapi.sdk.constant.HttpMethod;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.task.constant.ApiConstant;
import com.fls.task.constant.CommonConstant;
import com.fls.task.core.executor.ApiRequestExecutor;
import com.fls.task.core.executor.TaskExecutor;
import com.fls.task.core.handler.IApiTaskHandler;
import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.model.ApiRequestContext;
import com.fls.task.core.task.ApiTask;
import com.fls.task.entity.ApiDefInfoEntity;
import com.fls.task.entity.ApiTaskInfoEntity;
import com.fls.task.enums.ApiTaskStatusEnum;
import com.fls.task.pojo.model.OnceTaskInfo;
import com.fls.task.pojo.model.ResTableInfo;
import com.fls.task.pojo.model.TaskContext;
import com.fls.task.pojo.param.TriggerParam;
import com.fls.task.pojo.req.ApiTaskReq;
import com.fls.task.pojo.req.OnceTaskAddReq;
import com.fls.task.service.ApiDefInfoService;
import com.fls.task.service.ApiTaskInfoService;
import com.fls.task.service.ApiTaskService;
import com.fls.task.service.IResTableService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * API任务服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ApiTaskServiceImpl implements ApiTaskService {

    private final ApiTaskInfoService taskInfoService;

    private final ApiDefInfoService apiDefInfoService;

    private final ApiClientFacade apiClientFacade;

    private final Environment environment;

    private final ApiRequestExecutor apiRequestExecutor;

    private final TaskExecutor taskExecutor;

    private final IResTableService resTableService;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public <T extends IApiTaskHandler> String addDistributeTask(ApiTaskReq apiTaskReq, Class<T> clazz) {
        // 1. 参数校验
        validateTaskRequest(apiTaskReq, clazz);

        String beanName = StrUtil.lowerFirst(clazz.getSimpleName());
        apiTaskReq.setTaskHandler(beanName);

        // 2. 获取并验证接口定义
        ApiDefInfoEntity apiDefInfo = getAndValidateApiDef(apiTaskReq.getApiCode());

        // 3. 保存初始任务信息
        ApiTaskInfoEntity taskInfo = saveInitialTaskInfo(apiTaskReq, apiDefInfo, clazz);

        try {
            // 4. 提交到调度中心
            submitToScheduleCenter(taskInfo);

            // 5. 更新任务状态为准备执行
            taskInfo.setTaskStatus(ApiTaskStatusEnum.PREPARE.getCode());
            log.info("分布式任务提交成功, taskId: {}, handlerClass: {}", taskInfo.getIdApiTask(), clazz.getName());
            return taskInfo.getIdApiTask();

        } catch (Exception e) {
            log.error("分布式任务提交失败, taskId: {}, handlerClass: {}", taskInfo.getIdApiTask(), clazz.getName(), e);
            taskInfo.setTaskStatus(ApiTaskStatusEnum.FAIL.getCode());
            throw new RuntimeException("提交分布式任务失败: " + e.getMessage(), e);
        } finally {
            log.debug("分布式任务提交完成, task info: {}", taskInfo);
            taskInfoService.updateById(taskInfo);
        }
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public <T extends IApiTaskHandler> String addLocalTask(ApiTaskReq apiTaskReq, Class<T> clazz) {
        // 1. 参数校验
        validateTaskRequest(apiTaskReq, clazz);

        String beanName = StrUtil.lowerFirst(clazz.getSimpleName());
        apiTaskReq.setTaskHandler(beanName);

        // 2. 获取并验证接口定义
        ApiDefInfoEntity apiDefInfo = getAndValidateApiDef(apiTaskReq.getApiCode());

        // 3. 保存初始任务信息
        ApiTaskInfoEntity taskInfo = saveInitialTaskInfo(apiTaskReq, apiDefInfo, clazz);

        try {
            // 4. 提交到调度中心
            submitLocalOnceTask(taskInfo, apiDefInfo);

            // 5. 更新任务状态为准备执行
            taskInfo.setTaskStatus(ApiTaskStatusEnum.PREPARE.getCode());
            log.info("本地任务提交成功, taskId: {}, handlerClass: {}", taskInfo.getIdApiTask(), clazz.getName());
            return taskInfo.getIdApiTask();

        } catch (Exception e) {
            log.error("本地任务提交失败, taskId: {}, handlerClass: {}", taskInfo.getIdApiTask(), clazz.getName(), e);
            taskInfo.setTaskStatus(ApiTaskStatusEnum.FAIL.getCode());
            throw new RuntimeException("提交本地任务失败: " + e.getMessage(), e);
        } finally {
            log.debug("本地任务提交完成, task info: {}", taskInfo);
            taskInfoService.updateById(taskInfo);
        }
    }

    @Override
    public ITaskHandler buildApiTask(String idApiTask) {
        // 1. 加载任务上下文信息
        TaskContext taskContext = loadTaskContext(idApiTask);

        return buildApiTaskByContext(taskContext);
    }

    public ITaskHandler buildApiTaskByContext(TaskContext taskContext) {
        // 1. 构建API任务执行器
        ApiTask apiTask = new ApiTask();

        // 2. 设置请求上下文
        apiTask.setRequestContext(buildRequestContext(taskContext));

        // 3. 设置任务请求信息
        apiTask.setApiTaskReq(buildApiTaskReq(taskContext));

        // 4.设置task handler
        apiTask.setApiTaskHandler(buildApiTaskHandler(taskContext));

        // 5.注入service服务
        apiTask.setTaskInfoService(taskInfoService);
        apiTask.setApiRequestExecutor(apiRequestExecutor);
        return apiTask;
    }

    /**
     * 加载任务上下文信息
     */
    private TaskContext loadTaskContext(String idApiTask) {
        ApiTaskInfoEntity taskInfo = taskInfoService.getById(idApiTask);
        Assert.notNull(taskInfo, "未找到对应的任务信息，idApiTask: " + idApiTask);

        ApiDefInfoEntity apiDefInfo = apiDefInfoService.getById(taskInfo.getIdApiDef());
        Assert.notNull(apiDefInfo, "未找到对应的接口定义信息，idApiDef: " + taskInfo.getIdApiDef());

        return new TaskContext(taskInfo, apiDefInfo);
    }

    /**
     * 构建API请求上下文
     */
    private ApiRequestContext buildRequestContext(TaskContext context) {
        return ApiRequestContext.builder().taskId(context.getApiTaskInfo().getIdApiTask()).host(context.getApiDefInfo().getHost()).path(context.getApiDefInfo().getApiPath()).idBill(context.getApiTaskInfo().getIdSourceBill())
            .url(context.getApiDefInfo().getHost() + context.getApiDefInfo().getApiPath()).method(context.getApiTaskInfo().getReqMethod()).requestData(context.getApiTaskInfo().getReqBody()).apiType(context.getApiDefInfo().getApiType())
            .build();
    }

    /**
     * 构建API任务请求
     */
    private ApiTaskReq buildApiTaskReq(TaskContext context) {
        ApiTaskReq apiTaskReq = new ApiTaskReq();
        apiTaskReq.setApiCode(context.getApiDefInfo().getApiCode());
        apiTaskReq.setIdSource(context.getApiTaskInfo().getIdSourceBill());
        apiTaskReq.setCodeSource(context.getApiTaskInfo().getSourceBillCode());
        apiTaskReq.setIdSourceRes(context.getApiTaskInfo().getIdSourceRes());
        apiTaskReq.setRequestData(context.getApiTaskInfo().getReqBody());
        return apiTaskReq;
    }

    private IApiTaskHandler buildApiTaskHandler(TaskContext taskContext) {
        String handlerClassName = taskContext.getApiTaskInfo().getTaskHandler();
        IApiTaskHandler defaultApiTaskHandler = SpringUtil.getBean("defaultApiTaskHandler", IApiTaskHandler.class);

        if (StringUtils.isBlank(handlerClassName)) {
            log.warn("处理器类名为空，使用默认处理器，taskId: {}", taskContext.getApiTaskInfo().getIdApiTask());
            return defaultApiTaskHandler;
        }
        // 尝试从Spring容器中获取处理器Bean
        try {
            IApiTaskHandler taskHandler = SpringUtil.getBean(handlerClassName, IApiTaskHandler.class);
            return taskHandler == null ? defaultApiTaskHandler : taskHandler;
        } catch (BeanCreationException e) {
            log.error("类不存在: {}", handlerClassName);
        } catch (NoSuchBeanDefinitionException e) {
            log.error("Spring容器中没有该类型的Bean: {}", handlerClassName);
        } catch (Exception e) {
            log.error("通过类型获取Bean失败: {}", handlerClassName, e);
        }
        return defaultApiTaskHandler;
    }

    /**
     * 验证任务请求参数
     */
    private void validateTaskRequest(ApiTaskReq apiTaskReq, Class<?> clazz) {
        Assert.notNull(apiTaskReq, "任务请求参数不能为空");
        Assert.notNull(clazz, "任务处理器类不能为空");
        Assert.hasText(apiTaskReq.getApiCode(), "接口编码不能为空");
    }

    /**
     * 获取并验证API定义信息
     */
    private ApiDefInfoEntity getAndValidateApiDef(String apiCode) {
        ApiDefInfoEntity apiDefInfo = apiDefInfoService.getByApiCode(apiCode);
        Assert.notNull(apiDefInfo, "未找到对应的接口定义信息，apiCode: " + apiCode);
        return apiDefInfo;
    }

    /**
     * 保存初始任务信息
     */
    private ApiTaskInfoEntity saveInitialTaskInfo(ApiTaskReq apiTaskReq, ApiDefInfoEntity apiDefInfo, Class<?> clazz) {
        ApiTaskInfoEntity taskInfo = new ApiTaskInfoEntity();

        // 设置API定义相关信息
        taskInfo.setIdApiDef(apiDefInfo.getIdApiDef());
        taskInfo.setReqUrl(apiDefInfo.getHost() + apiDefInfo.getApiPath());
        taskInfo.setReqMethod(apiDefInfo.getApiMethod());
        String beanName = StrUtil.lowerFirst(clazz.getSimpleName());
        taskInfo.setTaskHandler(beanName);

        // 设置请求相关信息
        taskInfo.setReqBody(apiTaskReq.getRequestData());

        // 设置来源信息
        ResTableInfo resTableInfo = resTableService.queryResTableInfo(apiTaskReq.getIdSourceRes());
        if (resTableInfo != null) {
            taskInfo.setSourceBillName(resTableInfo.getName());
            taskInfo.setSourceBillTable(resTableInfo.getTblName());
        }
        taskInfo.setIdSourceBill(apiTaskReq.getIdSource());
        taskInfo.setSourceBillCode(apiTaskReq.getCodeSource());
        taskInfo.setIdSourceRes(apiTaskReq.getIdSourceRes());
        taskInfo.setAssetCode(apiTaskReq.getAssetCode());

        // 设置任务状态和创建时间
        taskInfo.setTaskStatus(ApiTaskStatusEnum.PREPARE.getCode());
        taskInfo.setCreateTime(LocalDateTime.now());

        String idUser = LoginHelper.getUserIdOrDefault();
        taskInfo.setCreator(idUser);
        String userName = StrUtil.equals(idUser, com.fls.framework.core.consts.CommonConstant.DEFAULT_USER_ID) ? "" : LoginHelper.getLoginUser().getName();
        taskInfo.setCreateName(userName);

        // 设置项目来源信息
        String projectCode = environment.getProperty("spring.application.name", "unknown");
        taskInfo.setSourceProjectCode(projectCode);
        taskInfo.setSourceProjectUrl(getProjectUrl());

        taskInfoService.save(taskInfo);
        return taskInfo;
    }

    /**
     * 提交任务到调度中心
     */
    private void submitToScheduleCenter(ApiTaskInfoEntity taskInfo) {
        OnceTaskAddReq onceTaskAddReq = new OnceTaskAddReq();
        onceTaskAddReq.setIdApiTask(taskInfo.getIdApiTask());
        onceTaskAddReq.setMisfireStrategy(CommonConstant.MIS_FIRE_STRATEGY);

        String resStr = apiClientFacade.invoke(HttpMethod.POST.getValue(), ApiConstant.ONCE_TASK_ADD, BeanUtil.beanToMap(onceTaskAddReq));

        ResponseData responseData = JSONUtil.toBean(resStr, ResponseData.class);
        if (!responseData.getSuccess()) {
            throw new RuntimeException("调度中心返回失败: " + responseData.getMsg());
        }
    }

    /**
     * 提交任务到调度中心
     */
    private void submitLocalOnceTask(ApiTaskInfoEntity taskInfo, ApiDefInfoEntity apiDefInfo) {
        String idApiTask = taskInfo.getIdApiTask();
        log.debug("开始提交本地一次性任务, taskId: {}", idApiTask);

        try {
            // 2. 创建任务信息
            OnceTaskInfo onceTaskInfo = new OnceTaskInfo();
            onceTaskInfo.setIdTask(idApiTask);

            // 3. 构建任务处理器
            TaskContext taskContext = new TaskContext(taskInfo, apiDefInfo);
            ITaskHandler taskHandler = buildApiTaskByContext(taskContext);
            onceTaskInfo.setTaskHandler(taskHandler);

            // 4. 设置触发参数
            TriggerParam triggerParam = new TriggerParam();
            triggerParam.setTimeout(CommonConstant.ONCE_TASK_TIMEOUT_SECOND);
            onceTaskInfo.setTriggerParam(triggerParam);

            // 5. 提交任务
            boolean submit = taskExecutor.submitOnceTask(idApiTask, onceTaskInfo);

            if (!submit) {
                log.error("本地一次性任务提交失败, taskId: {}", idApiTask);
                throw new RuntimeException("添加本地一次性任务失败，taskId: " + idApiTask);
            }

            log.info("本地一次性任务提交成功, taskId: {}", idApiTask);

        } catch (Exception e) {
            log.error("提交本地一次性任务异常, taskId: {}", idApiTask, e);
            throw new RuntimeException("提交本地一次性任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取项目URL（IP + 端口）
     */
    private String getProjectUrl() {
        try {
            String serverPort = environment.getProperty("server.port", "8080");
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            return CommonConstant.PREFIX_HTTP + hostAddress + ":" + serverPort;
        } catch (UnknownHostException e) {
            log.warn("获取本机IP地址失败", e);
            return "";
        }
    }
}
