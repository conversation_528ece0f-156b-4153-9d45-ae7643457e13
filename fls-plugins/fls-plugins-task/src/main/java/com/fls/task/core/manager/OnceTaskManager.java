package com.fls.task.core.manager;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.stereotype.Component;

import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.pool.OnceTaskThreadPool;
import com.fls.task.pojo.model.OnceTaskInfo;
import com.fls.task.pojo.param.TriggerParam;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 一次性任务管理器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OnceTaskManager {

    /**
     * 任务队列
     */
    private final BlockingQueue<OnceTaskInfo> taskQueue = new LinkedBlockingQueue<>();

    /**
     * 任务映射，key为idApiTask
     */
    private final ConcurrentMap<String, OnceTaskInfo> taskRepository = new ConcurrentHashMap<>();

    /**
     * 任务执行线程池
     */
    private final OnceTaskThreadPool taskExecutor;

    /**
     * 扫描执行线程池
     */
    private ScheduledExecutorService taskScanner;

    @PostConstruct
    public void init() {
        // 初始化任务扫描器
        taskScanner = new ScheduledThreadPoolExecutor(1, new BasicThreadFactory.Builder().namingPattern("once-task-%d").daemon(true).build(), new ThreadPoolExecutor.CallerRunsPolicy());

        // 启动扫描器
        taskScanner.scheduleWithFixedDelay(() -> {
            try {
                scanAndExecuteTasks();
            } catch (Exception e) {
                log.error("OnceTaskScanner error", e);
            }
        }, 1, 1, TimeUnit.SECONDS);
        log.info("OnceTaskManager initialized successfully");
    }

    /**
     * 提交一次性任务
     */
    public boolean submitTask(String taskId, OnceTaskInfo onceTask) {
        return submitTask(taskId, onceTask, 0);
    }

    /**
     * 提交一次性任务（支持延迟执行）
     */
    public boolean submitTask(String taskId, OnceTaskInfo onceTask, long delaySeconds) {
        try {
            taskQueue.offer(onceTask);
            taskRepository.put(taskId, onceTask);
            log.info("OnceTask submitted successfully, taskId:{}, delaySeconds:{}", taskId, delaySeconds);
            return true;
        } catch (Exception e) {
            log.error("OnceTask submit failed, taskId:{}", taskId, e);
            return false;
        }
    }

    /**
     * 取消一次性任务
     */
    public boolean cancelTask(String taskId) {
        OnceTaskInfo onceTask = taskRepository.get(taskId);
        if (onceTask != null && onceTask.getTaskHandler() != null) {
            try {
                onceTask.getTaskHandler().destroy();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            taskRepository.remove(taskId);
            log.info("OnceTask cancelled successfully, taskId:{}", taskId);
            return true;
        }
        return false;
    }

    /**
     * 获取任务状态
     */
    public OnceTaskInfo getTaskInfo(String taskId) {
        return taskRepository.get(taskId);
    }

    /**
     * 扫描并执行任务
     */
    private void scanAndExecuteTasks() {
        OnceTaskInfo onceTaskInfo;
        while ((onceTaskInfo = taskQueue.poll()) != null) {
            executeTask(onceTaskInfo);
        }
    }

    /**
     * 执行任务
     */
    private void executeTask(OnceTaskInfo onceTask) {
        // 直接提交到线程池并获取Future
        Future<Void> future = taskExecutor.putTaskWithTimeout(createTaskRunnable(onceTask), 0);

        // 处理超时（在当前线程中等待）
        TriggerParam triggerParam = onceTask.getTriggerParam();
        if (triggerParam != null && triggerParam.getTimeout() != null && triggerParam.getTimeout() > 0) {
            try {
                future.get(triggerParam.getTimeout(), TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("Task execution timeout after {} seconds", triggerParam.getTimeout());
                future.cancel(true);
            } catch (Exception e) {
                log.error("Task execution error", e);
            }
        }
    }

    /**
     * 创建任务执行逻辑
     */
    private Runnable createTaskRunnable(OnceTaskInfo onceTask) {
        return () -> {
            ITaskHandler taskHandler = onceTask.getTaskHandler();
            try {
                // 执行任务
                taskHandler.execute();
                log.info("OnceTask executed successfully, taskId:{}", onceTask.getIdTask());
            } catch (Exception e) {
                log.error("OnceTask execution failed, taskId:{}", onceTask.getIdTask(), e);
            } finally {
                // 立即清理资源 - 既然是一次性任务，执行完就应该清理
                taskRepository.remove(onceTask.getIdTask());
                log.debug("OnceTask cleaned from repository, taskId:{}", onceTask.getIdTask());
            }
        };
    }

    @PreDestroy
    public void destroy() throws Exception {
        log.info("OnceTaskManager destroying...");

        if (taskScanner != null && !taskScanner.isShutdown()) {
            taskScanner.shutdown();
            if (!taskScanner.awaitTermination(5, TimeUnit.SECONDS)) {
                taskScanner.shutdownNow();
            }
        }
        taskExecutor.shutdown();
        taskQueue.clear();
        taskRepository.clear();
        log.info("OnceTaskManager destroyed successfully");
    }
}
