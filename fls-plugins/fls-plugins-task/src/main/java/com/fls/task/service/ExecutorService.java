package com.fls.task.service;

import com.fls.task.pojo.param.TriggerParam;

/**
 * 执行器服务
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface ExecutorService {

    /**
     * 执行器触发执行
     *
     * @param triggerParam 触发参数
     * @return 操作结果
     */
    boolean run(TriggerParam triggerParam);

    /**
     * 执行器触发关闭任务
     *
     * @param triggerParam 触发参数
     * @return 执行结果
     */
    boolean kill(TriggerParam triggerParam);
}
