package com.fls.task.pojo.req;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 任务调度信息请求
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
public class OnceTaskAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接口任务id
     */
    @NotBlank(message = "接口任务id不能为空")
    private String idApiTask;

    /**
     * 调度过期策略
     */
    @NotBlank(message = "调度过期策略不能为空")
    private String misfireStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    private Integer executorTimeout;
}
