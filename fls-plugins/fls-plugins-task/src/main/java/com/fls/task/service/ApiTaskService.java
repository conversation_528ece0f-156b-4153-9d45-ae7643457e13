package com.fls.task.service;

import com.fls.task.core.handler.IApiTaskHandler;
import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.pojo.req.ApiTaskReq;

/**
 * 任务管理服务
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ApiTaskService {

    /**
     * 添加调度中心任务
     *
     * @param apiTaskReq 接口任务请求
     * @param clazz 类
     * @return idApiTask
     * @param <T> 泛型
     */
    <T extends IApiTaskHandler> String addDistributeTask(ApiTaskReq apiTaskReq, Class<T> clazz);

    /**
     * 添加本地任务
     *
     * @param apiTaskReq 接口任务请求
     * @param clazz 类型
     * @return idApiTask
     * @param <T> 泛型
     */
    <T extends IApiTaskHandler> String addLocalTask(ApiTaskReq apiTaskReq, Class<T> clazz);

    /**
     * 构建接口任务处理器
     *
     * @param idApiTask 接口任务id
     * @return 接口任务处理器
     */
    ITaskHandler buildApiTask(String idApiTask);
}
