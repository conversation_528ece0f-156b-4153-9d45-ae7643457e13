package com.fls.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 接口任务请求信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_api_task_info")
public class ApiTaskInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_api_task", type = IdType.ASSIGN_UUID)
    private String idApiTask;

    /**
     * 所属接口定义id
     */
    private String idApiDef;

    /**
     * 任务状态（待调度|进行中|执行成功|执行失败）
     */
    private String taskStatus;

    /**
     * 任务回调处理状态（成功|失败）
     */
    private String notifyStatus;

    /**
     * 请求url
     */
    private String reqUrl;

    /**
     * 请求方法
     */
    private String reqMethod;

    /**
     * 请求内容
     */
    private String reqBody;
    
    /**
     * 响应描述
     */
    private String resMsg;

    /**
     * 响应编码
     */
    private String resCode;

    /**
     * 响应内容
     */
    private String resText;

    /**
     * 任务耗时（单位：秒）
     */
    private Long taskCost;

    /**
     * 任务处理器名称
     */
    private String taskHandler;

    /**
     * 供应链任务id
     */
    private String idScccTask;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 来源项目编码
     */
    private String sourceProjectCode;

    /**
     * 来源项目地址
     */
    private String sourceProjectUrl;

    /**
     * 来源单据主键
     */
    private String idSourceBill;

    /**
     * 来源单据编号
     */
    private String sourceBillCode;

    /**
     * 来源单据资源主键
     */
    private String idSourceRes;

    /**
     * 来源单据名称
     */
    private String sourceBillName;

    /**
     * 来源单据表
     */
    private String sourceBillTable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

}
