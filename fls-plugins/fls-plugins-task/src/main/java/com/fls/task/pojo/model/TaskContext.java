package com.fls.task.pojo.model;

import com.fls.task.entity.ApiDefInfoEntity;
import com.fls.task.entity.ApiTaskInfoEntity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务上下文，用于封装任务相关的所有信息
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskContext {
    private ApiTaskInfoEntity apiTaskInfo;
    private ApiDefInfoEntity apiDefInfo;
}