package com.fls.task.controller;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaIgnore;

import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.task.pojo.param.TriggerParam;
import com.fls.task.service.ExecutorService;

import lombok.RequiredArgsConstructor;

/**
 * 执行器交互入口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@SaIgnore
@RestController
@RequestMapping("/executor")
@RequiredArgsConstructor
public class ExecutorController {

    private final ExecutorService executorService;

    /**
     * 执行器触发
     *
     * @param triggerParam 触发参数
     * @return 通用响应
     */
    @PostMapping("/run")
    public ResponseData run(@Validated() @RequestBody TriggerParam triggerParam) {
        return executorService.run(triggerParam) ? ResponseData.success() : ResponseData.error();
    }

    /**
     * 执行器终止
     *
     * @param triggerParam 触发参数
     * @return 通用响应
     */
    @PostMapping("/kill")
    public ResponseData kill(@Validated() @RequestBody TriggerParam triggerParam) {
        return executorService.kill(triggerParam) ? ResponseData.success() : ResponseData.error();
    }
}
