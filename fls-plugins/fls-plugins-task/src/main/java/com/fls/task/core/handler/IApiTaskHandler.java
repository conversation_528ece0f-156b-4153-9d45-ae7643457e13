package com.fls.task.core.handler;

import com.fls.task.core.model.ApiRequestContext;
import com.fls.task.core.model.ApiResponse;
import com.fls.task.pojo.req.ApiTaskReq;

/**
 * API任务处理器接口，继承自基础任务处理器，专门用于处理API类型的任务
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface IApiTaskHandler {
    /**
     * 请求前置处理，用于处理请求参数等
     *
     * @param context API请求上下文
     */
    default void beforeRequest(ApiRequestContext context) {}

    /**
     * 响应后置处理，用于处理响应结果
     *
     * @param context API请求上下文
     * @param response API响应结果
     * @return 处理后的响应结果
     */
    default ApiResponse afterRequest(ApiRequestContext context, ApiResponse response) {
        return response;
    }

    /**
     * API任务回调处理
     *
     * @param apiTaskReq API任务请求
     * @param response API响应结果
     */
    void handleCallback(ApiTaskReq apiTaskReq, ApiResponse response);

    /**
     * 重写父接口的重试策略
     *
     * @param e 异常
     * @return 是否支持重试
     */
    default boolean shouldRetry(Exception e) {
        return false;
    }
}