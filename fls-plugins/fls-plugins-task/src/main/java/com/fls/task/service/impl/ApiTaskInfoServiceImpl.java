package com.fls.task.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.task.entity.ApiTaskInfoEntity;
import com.fls.task.mapper.ApiTaskInfoMapper;
import com.fls.task.service.ApiTaskInfoService;

/**
 * <p>
 * 接口任务请求信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class ApiTaskInfoServiceImpl extends ServiceImpl<ApiTaskInfoMapper, ApiTaskInfoEntity> implements ApiTaskInfoService {

}
