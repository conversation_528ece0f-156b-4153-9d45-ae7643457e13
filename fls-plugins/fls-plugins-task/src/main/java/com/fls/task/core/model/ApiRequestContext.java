package com.fls.task.core.model;

import lombok.Builder;
import lombok.Data;

/**
 * API请求上下文，封装API请求相关的信息
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
public class ApiRequestContext {
    /**
     * 请求目标地址
     */
    private String host;

    /**
     * 请求的path路径
     */
    private String path;

    /**
     * 请求url
     */
    private String url;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求参数
     */
    private String requestData;

    /**
     * API类型（0-服务总线，1-原生请求，2-链式任务）
     */
    private String apiType;

    /**
     * 超时时间
     */
    private Integer timeout;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 单据id
     */
    private String idBill;

    /**
     * 链式任务id
     */
    private String idScccTask;
}