package com.fls.task.core.executor;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.lisenter.TaskStateListener;
import com.fls.task.core.manager.OnceTaskManager;
import com.fls.task.core.manager.ScheduleTaskManager;
import com.fls.task.core.thread.TaskThread;
import com.fls.task.pojo.model.OnceTaskInfo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务执行器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component("customTaskExecutor")
@RequiredArgsConstructor
public class TaskExecutor implements DisposableBean, TaskStateListener {

    private final ScheduleTaskManager scheduleTaskManager;

    private final OnceTaskManager onceTaskManager;

    // ==================== 定时调度任务相关方法 ====================

    /**
     * 注册定时调度任务线程
     */
    public TaskThread registScheduleTaskThread(String taskScheduleId, ITaskHandler handler, String removeOldReason) {
        return scheduleTaskManager.registTaskThread(taskScheduleId, handler, this, removeOldReason);
    }

    /**
     * 移除定时调度任务线程
     */
    public TaskThread removeScheduleTaskThread(String taskScheduleId, String removeOldReason) {
        return scheduleTaskManager.removeTaskThread(taskScheduleId, removeOldReason);
    }

    /**
     * 加载定时调度任务线程
     */
    public TaskThread loadScheduleTaskThread(String taskScheduleId) {
        return scheduleTaskManager.loadTaskThread(taskScheduleId);
    }

    // ==================== 一次性任务相关方法 ====================

    /**
     * 提交一次性任务
     */
    public boolean submitOnceTask(String taskId, OnceTaskInfo onceTask) {
        return onceTaskManager.submitTask(taskId, onceTask);
    }

    /**
     * 提交一次性任务（支持延迟执行）
     */
    public boolean submitOnceTask(String taskId, OnceTaskInfo onceTask, long delaySeconds) {
        return onceTaskManager.submitTask(taskId, onceTask, delaySeconds);
    }

    /**
     * 取消一次性任务
     */
    public boolean cancelOnceTask(String taskId) {
        return onceTaskManager.cancelTask(taskId);
    }

    /**
     * 获取一次性任务状态
     */
    public OnceTaskInfo getOnceTaskInfo(String taskId) {
        return onceTaskManager.getTaskInfo(taskId);
    }

    // ==================== 生命周期管理 ====================

    @Override
    public void onTaskIdle(String taskSchedulerId, String reason) {
        removeScheduleTaskThread(taskSchedulerId, reason);
    }

    @Override
    public void destroy() throws Exception {
        log.info("TaskExecutor destroying...");

        // 销毁定时调度任务
        scheduleTaskManager.destroyAllTaskThreads();

        // 一次性任务管理器会通过@PreDestroy自动销毁
        log.info("TaskExecutor destroyed successfully");
    }
}
