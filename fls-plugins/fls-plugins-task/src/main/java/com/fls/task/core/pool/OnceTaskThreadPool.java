package com.fls.task.core.pool;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 一次性任务线程池
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
@Component
public class OnceTaskThreadPool {

    private static final int SIZE = 4;

    private static final int MAX_SIZE = 16;

    private static final int MAX_COUNT = 100000;

    private static final int THREAD_KEEPALIVE = 30;

    private final ThreadPoolExecutor tpExecutor;

    public OnceTaskThreadPool() {
        this(SIZE, MAX_SIZE, MAX_COUNT, THREAD_KEEPALIVE);
    }

    public OnceTaskThreadPool(int size, int max, int count, int keepaliveTime) {
        tpExecutor = new ThreadPoolExecutor(size, max, keepaliveTime, TimeUnit.SECONDS, new LinkedBlockingQueue<>(count), new BasicThreadFactory.Builder().namingPattern("task-pool-%d").daemon(true).build(), new MyExecutorPolicy());
    }

    /**
     * 提交普通任务
     */
    public void putTask(Runnable task) {
        if (log.isDebugEnabled()) {
            log.debug("put task to executor.");
        }

        try {
            tpExecutor.execute(task);
        } catch (Exception e) {
            log.error("putTask Fail: ", e);
        }
    }

    /**
     * 提交支持超时的任务
     *
     * @param task 任务
     * @param timeoutSeconds 超时时间（秒），0或负数表示不限制超时
     * @return Future对象，可用于获取执行结果或取消任务
     */
    public Future<Void> putTaskWithTimeout(Runnable task, int timeoutSeconds) {
        return CompletableFuture.runAsync(task, tpExecutor);
    }

    public int getTaskCount() {
        return tpExecutor.getQueue().size();
    }

    public int getActiveThreadCount() {
        return tpExecutor.getActiveCount();
    }

    public void shutdown() throws Exception {
        if (tpExecutor != null && !tpExecutor.isShutdown()) {
            tpExecutor.shutdown();
            if (!tpExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                tpExecutor.shutdownNow();
            }
        }
    }

    public static class MyExecutorPolicy implements RejectedExecutionHandler {
        /**
         * Creates a {@code DiscardPolicy}.
         */
        MyExecutorPolicy() {}

        /**
         * Does nothing, which has the effect of discarding task r.
         *
         * @param r the runnable task requested to be executed
         * @param e the executor attempting to execute this task
         */
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            log.error("Task {} rejected from {}", r, e);
        }
    }
}
