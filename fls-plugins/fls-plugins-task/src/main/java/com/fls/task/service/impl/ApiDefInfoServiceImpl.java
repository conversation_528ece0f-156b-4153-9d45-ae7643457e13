package com.fls.task.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.task.entity.ApiDefInfoEntity;
import com.fls.task.mapper.ApiDefInfoMapper;
import com.fls.task.service.ApiDefInfoService;

import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 接口定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class ApiDefInfoServiceImpl extends ServiceImpl<ApiDefInfoMapper, ApiDefInfoEntity> implements ApiDefInfoService {

    private final ApiDefInfoMapper apiDefInfoMapper;

    @Override
    public ApiDefInfoEntity getByApiCode(String apiCode) {
        LambdaQueryWrapper<ApiDefInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiDefInfoEntity::getApiCode, apiCode);
        return apiDefInfoMapper.selectOne(wrapper);
    }
}
