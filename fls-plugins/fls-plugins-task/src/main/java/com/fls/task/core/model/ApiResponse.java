package com.fls.task.core.model;

import lombok.Builder;
import lombok.Data;

/**
 * API响应对象，封装API响应相关的信息
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Builder
public class ApiResponse {
    /**
     * 接口任务id
     */
    private String idTask;
    /**
     * 任务状态（待调度|进行中|执行成功|执行失败）
     */
    private String taskStatus;
    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 原始响应内容
     */
    private String rawResponse;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code) || "0".equals(code);
    }
}