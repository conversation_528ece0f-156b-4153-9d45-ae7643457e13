package com.fls.task.core.task;

import cn.hutool.core.util.StrUtil;
import com.fls.task.constant.CommonConstant;
import com.fls.task.core.executor.ApiRequestExecutor;
import com.fls.task.core.handler.IApiTaskHandler;
import com.fls.task.core.handler.ITaskHandler;
import com.fls.task.core.model.ApiRequestContext;
import com.fls.task.core.model.ApiResponse;
import com.fls.task.entity.ApiTaskInfoEntity;
import com.fls.task.enums.ApiTaskStatusEnum;
import com.fls.task.pojo.req.ApiTaskReq;
import com.fls.task.service.ApiTaskInfoService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * API任务执行器，专注于任务执行流程控制
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Data
public class ApiTask implements ITaskHandler {

    private ApiRequestExecutor apiRequestExecutor;

    private ApiTaskInfoService taskInfoService;

    /**
     * 接口任务处理器
     */
    private IApiTaskHandler apiTaskHandler;

    private ApiRequestContext requestContext;

    private ApiTaskReq apiTaskReq;

    public ApiTask() {
    }

    @Override
    public void execute() throws Exception {
        ApiTaskInfoEntity taskInfo = new ApiTaskInfoEntity();
        taskInfo.setIdApiTask(requestContext.getTaskId());
        try {
            long startTime = System.currentTimeMillis();
            // 委托给具体的API任务处理器执行前置处理
            apiTaskHandler.beforeRequest(requestContext);
            // 委托给具体的API任务处理器执行后置处理
            ApiResponse response = apiRequestExecutor.execute(requestContext);
            response = apiTaskHandler.afterRequest(requestContext, response);
            //前置处理逻辑可能会更新请求参数
            taskInfo.setReqBody(requestContext.getRequestData());
            taskInfo.setIdScccTask(requestContext.getIdScccTask());
            taskInfo.setResCode(response.getCode());
            taskInfo.setResMsg(response.getMessage());
            taskInfo.setResText(truncateResponseText(response.getRawResponse()));
            taskInfo.setTaskStatus(response.getTaskStatus());
            // 委托给具体的API任务处理器执行回调
            apiTaskHandler.handleCallback(apiTaskReq, response);
            long cost = System.currentTimeMillis() - startTime;
            taskInfo.setTaskCost(cost);
            log.info("API任务执行成功, taskId: {}, cost: {}ms", requestContext.getTaskId(), cost);
        }
        catch (Exception e) {
            log.error("API任务执行失败, taskId: {}", requestContext.getTaskId(), e);
            taskInfo.setTaskStatus(ApiTaskStatusEnum.FAIL.getCode());
            throw e;
        }
        finally {
            taskInfoService.updateById(taskInfo);
        }
    }

    @Override
    public boolean shouldRetry(Exception e) {
        // 委托给具体的处理器决定重试策略
        return apiTaskHandler.shouldRetry(e);
    }

    private String truncateResponseText(String responseText) {
        if (StrUtil.isBlank(responseText)) {
            return responseText;
        }
        if (responseText.length() <= CommonConstant.MAX_RES_TEXT_LENGTH) {
            return responseText;
        }
        log.warn("响应文本超长，进行截断处理，原长度: {}, 截断后长度: {}", responseText.length(), CommonConstant.MAX_RES_TEXT_LENGTH);
        return responseText.substring(0, CommonConstant.MAX_RES_TEXT_LENGTH - 20) + "\n...[响应内容过长已截断]";
    }
}
