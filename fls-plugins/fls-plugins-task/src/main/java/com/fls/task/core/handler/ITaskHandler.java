package com.fls.task.core.handler;

/**
 * 基础任务处理器接口，定义任务处理的基本行为
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface ITaskHandler {
    /**
     * 任务执行方法
     *
     * @throws Exception 执行异常
     */
    void execute() throws Exception;

    /**
     * 任务初始化方法，在execute之前调用
     *
     * @throws Exception 初始化异常
     */
    default void init() throws Exception {}

    /**
     * 任务销毁方法，在execute之后调用
     *
     * @throws Exception 销毁异常
     */
    default void destroy() throws Exception {}

    /**
     * 任务重试策略
     *
     * @param e 异常
     * @return 是否支持重试
     */
    default boolean shouldRetry(Exception e) {
        return false;
    }
}