package com.fls.order.api;

import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.order.model.request.TaskAssigneeReq;
import com.fls.order.model.request.TaskTerminateReq;
import com.fls.order.model.request.TaskTransferReq;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工单通用api定义
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Validated
@RestController
@RequestMapping("/api/v1/workorder/task")
public interface WorkOrderApi {

    /**
     * 工单任务终止
     *
     * @param terminateReq 任务终止DTO
     * @return 通用响应
     */
    @PostMapping("/terminate")
    ResponseData terminate(@RequestBody TaskTerminateReq terminateReq);

    /**
     * 工单任务转办
     *
     * @param transferReq 转办任务DTO
     * @return 通用响应
     */
    @PostMapping("/transfer")
    ResponseData transfer(@RequestBody TaskTransferReq transferReq);

    /**
     * 更新工单任务办理人和协办人
     *
     * @param assigneeReq 工单任务办理人和协办人更新DTO
     * @return 通用响应
     */
    @PostMapping("/assignee/update")
    ResponseData assignee(@RequestBody TaskAssigneeReq assigneeReq);
}