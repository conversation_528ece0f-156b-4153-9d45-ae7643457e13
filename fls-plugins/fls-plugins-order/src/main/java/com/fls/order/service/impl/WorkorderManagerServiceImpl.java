package com.fls.order.service.impl;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.framework.core.util.JsonUtils;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.order.constant.WorkOrderConstants;
import com.fls.order.exception.WorkOrderException;
import com.fls.order.model.request.TaskAssigneeReq;
import com.fls.order.model.request.TaskTerminateReq;
import com.fls.order.model.request.TaskTransferReq;
import com.fls.order.model.response.TaskChangeRsp;
import com.fls.order.service.IWorkorderManagerService;
import jodd.net.HttpMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * WorkorderManagerServiceImpl
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkorderManagerServiceImpl implements IWorkorderManagerService {

    private final ApiClientFacade apiClientFacade;

    @Override
    public TaskChangeRsp terminate(TaskTerminateReq terminateReq) {
        log.info("开始终止工单任务, taskId: {}, operator: {}", terminateReq.getIdTask(), terminateReq.getOperator());
        try {
            TaskChangeRsp result = invokeWorkOrderApi(WorkOrderConstants.ApiPath.TERMINATE, terminateReq);
            log.info("工单任务终止成功, taskId: {}, result: {}", terminateReq.getIdTask(), result);
            return result;
        }
        catch (Exception e) {
            log.error("工单任务终止失败, taskId: {}", terminateReq.getIdTask(), e);
            throw new WorkOrderException(WorkOrderConstants.ErrorCode.TERMINATE_FAILED, "工单任务终止失败: " + e.getMessage());
        }
    }

    @Override
    public TaskChangeRsp transfer(TaskTransferReq transferReq) {
        log.info("开始转办工单任务, taskId: {}, operator: {}, transUserId: {}", 
                transferReq.getIdTask(), transferReq.getOperator(), transferReq.getTransUserId());
        try {
            TaskChangeRsp result = invokeWorkOrderApi(WorkOrderConstants.ApiPath.TRANSFER, transferReq);
            log.info("工单任务转办成功, taskId: {}, result: {}", transferReq.getIdTask(), result);
            return result;
        }
        catch (Exception e) {
            log.error("工单任务转办失败, taskId: {}", transferReq.getIdTask(), e);
            throw new WorkOrderException(WorkOrderConstants.ErrorCode.TRANSFER_FAILED, "工单任务转办失败: " + e.getMessage());
        }
    }

    @Override
    public TaskChangeRsp assignee(TaskAssigneeReq assigneeReq) {
        log.info("开始指派工单任务, taskId: {}, operator: {}, assignee: {}, coOperators: {}", 
                assigneeReq.getIdTask(), assigneeReq.getOperator(), 
                assigneeReq.getAssignee(), assigneeReq.getIdsCoOperator());
        try {
            TaskChangeRsp result = invokeWorkOrderApi(WorkOrderConstants.ApiPath.ASSIGNEE, assigneeReq);
            log.info("工单任务指派成功, taskId: {}, result: {}", assigneeReq.getIdTask(), result);
            return result;
        }
        catch (Exception e) {
            log.error("工单任务指派失败, taskId: {}", assigneeReq.getIdTask(), e);
            throw new WorkOrderException(WorkOrderConstants.ErrorCode.ASSIGNEE_FAILED, "工单任务指派失败: " + e.getMessage());
        }
    }

    private <T> TaskChangeRsp invokeWorkOrderApi(String apiPath, T request) {
        try {
            // 使用ObjectMapper替代JSONUtil，更好的性能和错误处理
            ObjectMapper objectMapper = JsonUtils.getObjectMapper();
            JSONObject requestJson = objectMapper.convertValue(request, JSONObject.class);
            String responseStr = apiClientFacade.invoke(HttpMethod.POST.name(), apiPath, requestJson);
            ResponseData response = objectMapper.readValue(responseStr, ResponseData.class);
            if (ResponseData.isNotSuccess(response)) {
                throw new WorkOrderException(WorkOrderConstants.ErrorCode.REQUEST_FAILED, response.getMsg());
            }
            return objectMapper.convertValue(response.getData(), TaskChangeRsp.class);
        }
        catch (JsonProcessingException e) {
            throw new WorkOrderException("JSON_PARSE_ERROR", "JSON解析失败: " + e.getMessage());
        }
    }
}
