package com.fls.order.model.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 任务终止请求
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTerminateReq {

    @NotBlank(message = "工单任务id不能为空")
    private String idTask;

    @NotBlank(message = "操作人用户id不能为空")
    @Length(max = 50, message = "操作人ID长度不能超过50")
    private String operator;

    @NotNull(message = "工单终止标记不能为空")
    @Range(min = 0, max = 1, message = "终止标记只能是0或1")
    private Integer terminate;

    @NotBlank(message = "操作说明不能为空")
    @Length(max = 500, message = "操作说明长度不能超过500")
    private String remark;
}