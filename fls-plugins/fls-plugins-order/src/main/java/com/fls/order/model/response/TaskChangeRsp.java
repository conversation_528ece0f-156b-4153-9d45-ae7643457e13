package com.fls.order.model.response;

import lombok.Data;

/**
 * 工单任务变更响应
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class TaskChangeRsp {
    /**
     * 流程实例id
     */
    private String idProcInst;
    /**
     * 流程任务id
     */
    private String idProcTask;
    /**
     * 办理人id列表
     */
    private String idsAssignee;
    /**
     * 候选人id列表
     */
    private String idsCandidate;
    /**
     * 协办人id列表
     */
    private String idsCoOperator;
    /**
     * 工单记录id
     */
    private String idWorkorderRecord;
    /**
     * 工单任务记录id
     */
    private String idWorkorderTaskRecord;
    /**
     * 工单任务状态，参考字典order_task_status
     */
    private String taskStatus;
    /**
     * 工单状态，参考字典order_status
     */
    private String orderStatus;
    /**
     * 工单任务资源id
     */
    private String idTaskResource;
}
