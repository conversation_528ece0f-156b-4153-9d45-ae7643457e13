package com.fls.order.model.request;

import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 任务办理人和协办人更新请求
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
public class TaskAssigneeReq {
    /**
     * 任务办理人id
     */
    @NotBlank(message = "任务办理人不能为空")
    private String assignee;

    /**
     * 任务协办人id列表
     */
    private List<String> idsCoOperator;

    /**
     * 工单任务id
     */
    @NotBlank(message = "工单任务id不能为空")
    private String idTask;

    /**
     * 操作类型，操作类型枚举，1：指派，2：认领
     */
    private Integer operateType;

    /**
     * 操作人id
     */
    @NotBlank(message = "操作人用户id不能为空")
    private String operator;

    /**
     * 要求完成时间
     */
    private LocalDateTime deadline;
}