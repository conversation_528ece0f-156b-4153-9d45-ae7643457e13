package com.fls.order.constant;

/**
 * 常量管理
 *
 * <AUTHOR>
 * @since 2025/8/12
 */
public interface WorkOrderConstants {

    /**
     * API路径常量
     */
    interface ApiPath {
        String BASE_PATH = "/api/v1/workorder/task";
        String TERMINATE = BASE_PATH + "/terminate";
        String TRANSFER = BASE_PATH + "/transfer";
        String ASSIGNEE = BASE_PATH + "/assignee/update";
    }

    /**
     * 错误码常量
     */
    interface ErrorCode {
        String REQUEST_FAILED = "REQ_FAILED";
        String TRANSFER_FAILED = "TRANSFER_FAILED";
        String TERMINATE_FAILED = "TERMINATE_FAILED";
        String ASSIGNEE_FAILED = "ASSIGNEE_FAILED";
    }

    /**
     * 业务常量
     */
    interface Business {
        int TERMINATE_SKIP = 0; // 跳过任务继续执行
        int TERMINATE_STOP = 1; // 终止工单流程
    }
}