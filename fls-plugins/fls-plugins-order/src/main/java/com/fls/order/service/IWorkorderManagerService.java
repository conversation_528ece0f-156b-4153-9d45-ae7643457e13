package com.fls.order.service;

import com.fls.order.model.request.TaskAssigneeReq;
import com.fls.order.model.request.TaskTerminateReq;
import com.fls.order.model.request.TaskTransferReq;
import com.fls.order.model.response.TaskChangeRsp;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工单管理服务类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IWorkorderManagerService {

    /**
     * 终止工单任务
     *
     * @param terminateReq 终止任务请求
     * @return 服务总线操作结果
     */
    TaskChangeRsp terminate(@Valid @RequestBody TaskTerminateReq terminateReq);

    /**
     * 转办工单任务
     *
     * @param transferReq 转办请求
     * @return 服务总线操作结果
     */
    TaskChangeRsp transfer(@Valid @RequestBody TaskTransferReq transferReq);

    /**
     * 指派工单任务
     *
     * @param assigneeReq 指派请求
     * @return 服务总线操作结果
     */
    TaskChangeRsp assignee(@Valid @RequestBody TaskAssigneeReq assigneeReq);
}