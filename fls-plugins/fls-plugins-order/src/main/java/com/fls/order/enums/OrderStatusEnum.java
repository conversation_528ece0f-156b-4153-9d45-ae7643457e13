package com.fls.order.enums;

import lombok.Getter;

/**
 * 工单状态枚举
 *
 * <AUTHOR>
 * @since 2025/8/21
 */
@Getter
public enum OrderStatusEnum {
    /**
     * 初制单
     */
    INIT(0, "初制单"),
    PROGRESS(1, "进行中"),
    TERMINATE(2, "已终止"),
    END(3, "已完成");

    private final int code;
    private final String status;

    OrderStatusEnum(int code, String status) {
        // 成员变量
        this.code = code;
        this.status = status;
    }
}
