package com.fls.order.model.request;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工单任务转办DTO
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
public class TaskTransferReq {
    /**
     * 工单任务记录id
     */
    @NotBlank(message = "工单任务id不能为空")
    private String idTask;

    /**
     * 操作人用户id
     */
    @NotBlank(message = "操作人用户id不能为空")
    private String operator;

    /**
     * 转办人用户id
     */
    @NotBlank(message = "转办人id不能为空")
    private String transUserId;

    /**
     * 操作说明
     */
    @NotBlank(message = "操作说明不能为空")
    private String remark;

    /**
     * 是否是管理员操作
     */
    private Boolean isAdmin = false;
}