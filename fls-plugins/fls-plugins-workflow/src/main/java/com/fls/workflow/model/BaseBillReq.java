package com.fls.workflow.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * BillRequest
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Data
public class BaseBillReq {

    /**
     * 前置拦截器
     */
    String preInterceptor;

    /**
     * 后置拦截器
     */
    String postInterceptor;

    /**
     * 单据资源编码
     */
    @NotBlank(message = "单据资源编码不能为空")
    private String billCode;

    /**
     * 交易类型编码
     */
    private String transTypeCode;

    /**
     * 单据资源数据
     */
    private Object data;
}
