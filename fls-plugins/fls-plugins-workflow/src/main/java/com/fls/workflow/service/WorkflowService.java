package com.fls.workflow.service;

import cn.hutool.core.util.ObjectUtil;
import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.util.StringUtils;
import com.fls.workflow.context.ApplicationContextHolder;
import com.fls.workflow.interceptor.FlowInterceptor;
import com.fls.workflow.model.BaseBillReq;
import org.springframework.stereotype.Service;

/**
 * 工作流服务类，中间层服务，主要为了添加拦截器逻辑
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@Service
public class WorkflowService {

    public Object invokeWithProcessEnhancement(String api, BaseBillReq request) {
        return invokeWithProcessEnhancement(api, request, Object.class);
    }

    public <T> T invokeWithProcessEnhancement(String api, BaseBillReq request, Class<T> returnType) {
        String preInterceptor = request.getPreInterceptor();
        String postInterceptor = request.getPostInterceptor();
        try {
            // 1. 调用前置拦截器
            if (StringUtils.isNotEmpty(preInterceptor)) {
                executeInterceptor(preInterceptor, request);
            }
            // 2. 服务总线调用工作流能力接口

            // 3. 调用后置拦截器
            if (StringUtils.isNotEmpty(postInterceptor)) {
                executeInterceptor(postInterceptor, request);
            }

            // 4. 将结果转换为指定的类型并返回
            return returnType.cast(null);

        } catch (Exception e) {
            throw new ServiceException("工作流服务接口异常，请稍后再试");
        }
    }

    // 动态调用拦截器方法
    private static void executeInterceptor(String interceptorName, BaseBillReq request) {
        // 假设拦截器类是同一个类的静态方法，也可以根据需要做不同处理
        FlowInterceptor interceptor = ApplicationContextHolder.getBean(interceptorName);
        if (ObjectUtil.isNotNull(interceptor)) {
            // 调用拦截器方法
            interceptor.intercept(request);
        }
    }
}
