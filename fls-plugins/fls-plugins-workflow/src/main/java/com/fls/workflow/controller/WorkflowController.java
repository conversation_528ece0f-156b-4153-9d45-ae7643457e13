package com.fls.workflow.controller;

import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.workflow.model.BaseBillFlowApproveReq;
import com.fls.workflow.model.BaseBillFlowReq;
import com.fls.workflow.model.BillFlowTodoReq;
import com.fls.workflow.model.TransferReq;
import com.fls.workflow.service.WorkflowService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 工作流web入口
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@RequestMapping("/workflow")
public class WorkflowController {

    @Resource
    private WorkflowService workflowService;

    @PostMapping("/start")
    public ResponseData startProcess(@RequestBody @Valid BaseBillFlowReq req) {
        return ResponseData.success(workflowService.invokeWithProcessEnhancement("", req));
    }

    @PostMapping("/approve")
    public ResponseData approveProcess(@RequestBody @Valid BaseBillFlowApproveReq req) {
        return ResponseData.success(workflowService.invokeWithProcessEnhancement("", req));
    }

    @PostMapping("/todo")
    public ResponseData todo(@RequestBody BillFlowTodoReq req) {
        return ResponseData.success();
    }

    @GetMapping("/detail")
    public ResponseData historicTaskList(String processInstanceId) throws Exception {
        return ResponseData.success();
    }

    @GetMapping("/revoke")
    public ResponseData revoke(@RequestParam(required = true) String processInstanceId) {
        return ResponseData.success();
    }

    @PostMapping("/voided")
    public ResponseData voided(@RequestBody @Valid BaseBillFlowApproveReq req) {
        return ResponseData.success(workflowService.invokeWithProcessEnhancement("", req));
    }

    @PostMapping("/rejectback")
    public ResponseData rejectback(@RequestBody @Valid BaseBillFlowApproveReq req) {
        return ResponseData.success(workflowService.invokeWithProcessEnhancement("", req));
    }

    @PostMapping("/list")
    public ResponseData historicListData(@RequestBody BillFlowTodoReq req) {
        return ResponseData.success();
    }

    @GetMapping("/status")
    public ResponseData getProcessStatus(@RequestParam(required = true) String processInstanceId) {
        return ResponseData.success();
    }

    @PostMapping("/transfer")
    public ResponseData transferTask(@RequestBody @Valid TransferReq transferReq) {
        return ResponseData.success();
    }

    @GetMapping("/assignees")
    public ResponseData getAssignees(@RequestParam(required = true) String processInstanceId) {
        return ResponseData.success();
    }
}
