<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fls.system.mapper.ApproveMapper">
    <update id="updateResAudit">
        UPDATE ${approve.resourceInfo.table}
        SET
        <if test="approve.auditTime != null">
            audit_time = #{approve.auditTime},
        </if>
        <if test="approve.auditor != null and approve.auditor != ''">
            auditor = #{approve.auditor},
        </if>
        <if test="approve.idProcessInstance != null and approve.idProcessInstance != ''">
            id_process_instance = #{approve.idProcessInstance},
        </if>
        <if test="approve.status != null and approve.status != ''">
            status = #{approve.status}
        </if>
        WHERE ${approve.resourceInfo.idColumn} = #{approve.resourceInfo.idObj}
    </update>
    <select id="queryResAudit" resultType="com.fls.system.model.ResourceApproveInfo">
        SELECT ${idColumn}, auditor, audit_time, status, id_process_instance
        FROM ${table}
        WHERE ${idColumn} = #{id}
    </select>
</mapper>