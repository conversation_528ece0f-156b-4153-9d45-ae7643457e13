#spring相关配置
spring:
  mvc:
    hiddenmethod:
      filter:
        enabled: true
    pathmatch:
      # 配置策略
      matching-strategy: ant-path-matcher
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 100MB
  #jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    locale: zh_CN
    serialization:
      # 格式化输出
      indent_output: false
    default-property-inclusion: non_null

#mybaits相关配置
mybatis-plus:
  typeAliasesPackage: com.fls.**.entity
  mapper-locations: classpath*:com/fls/ioa/**/*Mapper.xml, classpath:/META-INF/modeler-mybatis-mappings/*.xml,classpath*:mapper/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
    db-config:
      id-type: assign_id
      table-underline: true
    enable-sql-runner: true
  configuration-properties:
    prefix:
    #如果数据库为postgresql，则需要配置为blobType: BINARY
    blobType: BLOB
    #如果数据库为oracle或mssql，则需要配置为boolValue: 1
    boolValue: true

#Sa-Token 配置 (文档: https://sa-token.cc)
# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期  单位: 秒
  timeout: 36000
  # token临时有效期 (指定时间无操作就过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: fls-cloud@2022
  # 是否输出操作日志
  is-log: true
#应用配置
app:
  minop:
    #小程序请求模块开头url
    start-url: /minop
    # jwt秘钥
    secret: fls@#$%$^&^*&(*()_!~$%$%^%*^&(&*(*))__)+*^$&$%@$$!$