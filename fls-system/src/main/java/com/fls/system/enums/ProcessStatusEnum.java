package com.fls.system.enums;

import lombok.Getter;

/**
 * 流程响应状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Getter
public enum ProcessStatusEnum {

    /**
     * 进行中
     */
    INGRESS(1, "进行中"),
    /**
     * 已完成
     */
    END(2, "已完成");

    /**
     * 状态码
     */
    private final int code;

    /**
     * 状态名称
     */
    private final String name;

    ProcessStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

}
