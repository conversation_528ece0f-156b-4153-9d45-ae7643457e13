package com.fls.system.interceptor;

import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.framework.security.util.LoginHelper;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.model.ComponentRouteContext;
import com.fls.system.util.HttpHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;

/**
 * 拦截器，拦截配置的路径转发open-api服务
 *
 * <AUTHOR>
 * @date 2025/3/26 17:28
 */
public class ComponentRouteInterceptor implements HandlerInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComponentRouteInterceptor.class);

    private static final String URL_COMPONENT = "url-component";
    private static final String AUTH_TAG_USER_ID = "userId";
    private static final String AUTH_TAG_HREF = "href";

    //open-api前缀
    private static final String API_PREFIX = "/api";


    @Resource
    private ApiClientFacade apiClientFacade;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 请求是post并且handler是HandlerMethod，说明有对应的Controller方法，直接放行
        if (!"POST".equalsIgnoreCase(request.getMethod()) || handler instanceof HandlerMethod) {
            return true;
        }

        // 获取请求路径
        String requestUri = request.getRequestURI();

        // 以api开头的转发到open-ai服务
        if (requestUri.startsWith(API_PREFIX)) {
            LOGGER.info("拦截请求 | 请求路径={} | 转发到open-api路径={} ", requestUri, requestUri);
            executeForward(request, response, requestUri);
            // 拦截请求，不继续执行后续逻辑
            return false;
        }

        // 匹配失败，不拦截
        return true;
    }

    /**
     * 执行请求转发
     *
     * @param request
     * @param response
     */
    private void executeForward(HttpServletRequest request, HttpServletResponse response, String requestUri) {
        try {
            doExecute(new ComponentRouteContext(request, response, requestUri));
        } catch (Exception e) {
            LOGGER.error("转发请求失败:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void doExecute(ComponentRouteContext ctx) throws IOException {
//      获取body参数
        getBody(ctx);
//      执行转发
        String responseStr = doExchange(ctx);
//      构建返回
        buildResponse(ctx.getResponse(), responseStr);
    }

    private String doExchange(ComponentRouteContext ctx) {
//        执行程序
        return apiClientFacade.invoke(ctx.getMethod(), ctx.getRequestUri(), ctx.getBodyMap());
    }


    private void buildResponse(HttpServletResponse response, String responseStr) throws IOException {
        ResponseData responseData = JSONUtil.toBean(responseStr, ResponseData.class, true);
        response.setStatus(responseData.getCode());
        response.setContentType(ContentType.JSON.getValue());
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(responseStr);
    }

    private Map<String, Object> getBody(ComponentRouteContext ctx) {
        Map<String, Object> bodyMap = HttpHelper.getBodyMap(ctx.getRequest());
//        获取路径
        String href = Optional.ofNullable(ctx.getRequest().getHeader(URL_COMPONENT))
                .orElse(ctx.getRequestUri());
//        获取用户id
        String userId = "";
        try {
            userId = LoginHelper.getLoginUser().getIdUser();
        } catch (Exception e) {
            LOGGER.error("获取用户异常", e);
        }
        bodyMap.put(AUTH_TAG_USER_ID, userId);
        bodyMap.put(AUTH_TAG_HREF, href);
        ctx.setBodyMap(bodyMap);
        LOGGER.info("转发body参数:{}", bodyMap);
        return bodyMap;
    }

}
