package com.fls.system.consts;

public interface ApiConstant {

    /**
     * 用户登录
     */
    String API_UPMS_USER_LOGIN = "/api/v1/system/login";

    /**
     * 缓存用户登录
     */
    String API_UPMS_USER_CACHE_LOGIN = "/api/v1/system/cache-login";

    /**
     * 获取用户信息
     */
    String API_UPMS_USER_LOGOUT = "/api/v1/system/logout";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_SET_AUTH = "/api/v1/auth/set-auth";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_AUTH = "/api/v1/auth/get-auth";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_ORG_AUTH = "/api/v1/auth/org";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_POST_AUTH = "/api/v1/auth/post";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_DEPT_AUTH = "/api/v1/auth/dept";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_RESOURCE_AUTH = "/api/v1/auth/doc";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_MENU = "/api/v1/auth/menu";

    /**
     * 获取用户数据权限
     */
    String API_UPMS_GET_PROJECT_MENU = "/api/v1/auth/menu-app";

    /**
     * 获取资源编码信息
     */
    String API_RESOURCE_CODE = "/api/v1/mdms/resource/code";

    /**
     * 获取资源信息
     */
    String API_RESOURCE_DETAIL = "/api/v1/mdms/resource/detail";

    /**
     * 获取资源信息
     */
    String API_RESOURCE_CODE_UPDATE = "/api/v1/mdms/resource/flow";

    /**
     * 仓库库存现存量查询
     */
    String WAREHOUSE_INVENTORY_HAND_QUANTITY_QUERY = "/api/v1/mdms/warehouse/extand";

    /**
     * 物料仓库存量查询
     */
    String MATERIAL_WAREHOUSE_INVENTORY_INQUIRY = "/api/v2/mdms/warehouse/extand";

    /**
     * 字典值查询
     */
    String API_DICT_VALUE_QUERY = "/api/v1/mdms/dict/value";
}
