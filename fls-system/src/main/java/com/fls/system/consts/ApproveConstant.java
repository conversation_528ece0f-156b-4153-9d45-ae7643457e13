package com.fls.system.consts;

/**
 * 审批流程常量定义
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface ApproveConstant {

    /**
     * 审批同意操作结果
     */
    int APPROVE_AGREE_RESULT = 1;

    /**
     * 审批不同意操作结果
     */
    int APPROVE_DISAGREE_RESULT = 4;

    /**
     * 发起流程
     */
    String API_PROCESS_START = "/api/v1/workflow/process";

    /**
     * 审批流程
     */
    String API_PROCESS_APPROVE = "/api/v1/workflow/approve";

    /**
     * 审批不同意
     */
    String API_PROCESS_DISAGREE = "/api/v1/workflow/approve/disagree";

    /**
     * 撤销流程
     */
    String API_PROCESS_REVOKE = "/api/v1/workflow/revoke";

    /**
     * 作废流程
     */
    String API_PROCESS_VOIDED = "/api/v1/workflow/voided";

    /**
     * 驳回上一级流程
     */
    String API_PROCESS_REJECT_PREVIOUS = "/api/v1/workflow/reject/back";

    /**
     * 驳回发起人流程
     */
    String API_PROCESS_REJECT_APPLICANT = "/api/v1/workflow/reject/applicant";

    /**
     * 流程转办
     */
    String API_PROCESS_REJECT_TRANSFER = "/api/v1/workflow/transfer";

    /**
     * 流程历史
     */
    String API_PROCESS_HISTORY = "/api/v1/workflow/detail";

    /**
     * 待审任务
     */
    String API_PROCESS_TASKS = "/api/v1/workflow/tasks";

    /**
     * 下一待审任务
     */
    String API_PROCESS_NEXT_TASK = "/api/v1/workflow/task/next";

    /**
     * 默认的提交申请节点名称
     */
    String SUBMIT_NODE_NAME = "提交申请";
}
