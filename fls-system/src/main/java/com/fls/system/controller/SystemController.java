package com.fls.system.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import com.fls.framework.core.pojo.login.LoginUser;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.system.service.IAuthService;
import com.fls.system.service.ILoginService;

@RestController
@RequestMapping("/system")
@ConditionalOnProperty(prefix = "project", name = "system-api-enable", havingValue = "true", matchIfMissing = false)
public class SystemController {

    @Resource
    private ILoginService loginService;

    @Resource
    private IAuthService authService;

    @PostMapping({"/login"})
    public ResponseData login(@RequestBody Dict dict) {
        String username = dict.getStr("username");
        String password = dict.getStr("password");
        if (StrUtil.isBlank(username) || StrUtil.isBlank(password)) {
            return ResponseData.error("用户名或密码不能为空");
        }
        LoginUser loginUser = loginService.login(username, password);
        String token = StpUtil.getTokenValue();
        Map<String, Object> result = MapUtil.builder(new HashMap<String, Object>()).put("loginUser", loginUser).put("token", token).build();
        return ResponseData.success(result);
    }

    @PostMapping({"/logout"})
    public ResponseData logout() {
        loginService.logout();
        return ResponseData.success();
    }

    @PostMapping({"/menu"})
    public ResponseData getMenu() {
        return authService.getAuthMenu();
    }

    @PostMapping({"/login-with-ticket"})
    public ResponseData getMenu(@RequestBody Dict dict) {
        String ticket = dict.getStr("ticket");
        if (StrUtil.isBlank(ticket)) {
            return ResponseData.error("系统票据不能为空");
        }
        LoginUser loginUser = loginService.loginWithTicket(ticket);
        String token = StpUtil.getTokenValue();
        Map<String, Object> result = MapUtil.builder(new HashMap<String, Object>()).put("loginUser", loginUser).put("token", token).build();
        return ResponseData.success(result);
    }
}