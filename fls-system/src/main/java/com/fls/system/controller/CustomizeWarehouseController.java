package com.fls.system.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.consts.ApiConstant;
import com.fls.system.model.MmsResult;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * MMS应用接口代理控制器，用于处理mms接口请求响应，
 *  TODO：注：现在实现的功能主要是格式化响应结构，这个功能不应该在sdk实现，统一响应是open-api的责任，这里先单独做映射处理
 *
 * <AUTHOR>
 * @date 2025/4/1 15:45
 */
@RestController
@RequestMapping("/api")
@ConditionalOnProperty(prefix = "project", name = "warehouse-api-enable", havingValue = "true", matchIfMissing = true)
public class CustomizeWarehouseController {

    @Resource
    private ApiClientFacade apiClientFacade;

    /**
     * 仓库库存现存量查询
     *
     * @return
     */
    @PostMapping("/v1/mdms/warehouse/extand")
    private ResponseData v1Extand(@RequestBody Map<String, Object> paramMap) {
        MmsResult mmsResult = exchange(ApiConstant.WAREHOUSE_INVENTORY_HAND_QUANTITY_QUERY, paramMap);
        return build(paramMap,mmsResult);
    }


    /**
     * 物料仓库存量查询
     *
     * @return
     */
    @PostMapping("/v2/mdms/warehouse/extand")
    private ResponseData v2Extand(@RequestBody Map<String, Object> paramMap) {
        MmsResult mmsResult = exchange(ApiConstant.MATERIAL_WAREHOUSE_INVENTORY_INQUIRY, paramMap);
        return build(paramMap,mmsResult);
    }

    private ResponseData build(Map<String, Object> paramMap,MmsResult mmsResult){
        Integer pageNo = MapUtil.getInt(paramMap, "pageNo", 1);
        Integer pageSize = MapUtil.getInt(paramMap, "pageSize", 20);
        return new ResponseData(mmsResult.getSuccess(),
                mmsResult.getCode(),
                mmsResult.getMsg(),
                mmsResult.toPageResult(pageNo, pageSize));
    }

    private MmsResult exchange(String url, Map<String, Object> paramMap) {
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), url, paramMap);
        return JSONUtil.toBean(result, MmsResult.class);
    }
}
