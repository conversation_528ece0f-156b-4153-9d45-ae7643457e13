package com.fls.system.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.system.consts.ValidationGroups;
import com.fls.system.handler.ApproveProcessHandler;
import com.fls.system.model.request.ApproveRequest;
import com.fls.system.model.request.BaseBillRequest;
import com.fls.system.model.response.ApproveHistory;

/**
 * 审批流程通用Web入口
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@RequestMapping("/approve")
@ConditionalOnProperty(prefix = "project", name = "system-api-enable", havingValue = "true")
public class ApproveController {

    @Resource
    private ApproveProcessHandler approveProcessHandler;

    /**
     * 提交流程
     *
     * @param billRequest 通用流程请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/submit")
    public ResponseData submit(@Validated(ValidationGroups.Submit.class) @RequestBody BaseBillRequest billRequest) {
        approveProcessHandler.submit(billRequest);
        return ResponseData.success();
    }

    /**
     * 重试审批同意回调
     *
     * @param billRequest 通用流程请求
     * @return 操作结果
     */
    @PostMapping("/retry")
    public ResponseData retryAfterAgree(@Validated(ValidationGroups.Retry.class) @RequestBody BaseBillRequest billRequest) {
        approveProcessHandler.retryAfterAgree(billRequest);
        return ResponseData.success();
    }

    /**
     * 审批同意
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/agree")
    public ResponseData agree(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.agree(approveRequest);
        return ResponseData.success();
    }

    /**
     * 审批不同意
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/disagree")
    public ResponseData disagree(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.disAgree(approveRequest);
        return ResponseData.success();
    }

    /**
     * 驳回制单人
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/reject/initiator")
    public ResponseData rejectToInitiator(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.rejectToInitiator(approveRequest);
        return ResponseData.success();
    }

    /**
     * 驳回上一级
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/reject/previous")
    public ResponseData rejectPrevious(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.rejectPrevious(approveRequest);
        return ResponseData.success();
    }

    /**
     * 撤销提交
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/revoke")
    public ResponseData revoke(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.revoke(approveRequest);
        return ResponseData.success();
    }

    /**
     * 作废流程
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/voided")
    public ResponseData voided(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.voided(approveRequest);
        return ResponseData.success();
    }

    /**
     * 转签
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/transfer")
    public ResponseData transfer(@Validated(ValidationGroups.Transfer.class) @RequestBody ApproveRequest approveRequest) {
        approveProcessHandler.transfer(approveRequest);
        return ResponseData.success();
    }

    /**
     * 流程流转记录
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/history")
    public ResponseData history(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        List<ApproveHistory> history = approveProcessHandler.history(approveRequest);
        return ResponseData.success(history);
    }

    /**
     * 获取待审任务列表
     *
     * @param approveRequest 审批请求
     * @return ResponseData 操作结果
     */
    @PostMapping("/tasks")
    public ResponseData getTasks(@Validated(ValidationGroups.Approve.class) @RequestBody ApproveRequest approveRequest) {
        return ResponseData.success(approveProcessHandler.getTasks(approveRequest));
    }

}