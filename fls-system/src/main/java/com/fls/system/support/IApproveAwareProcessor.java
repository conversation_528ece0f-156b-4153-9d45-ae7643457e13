package com.fls.system.support;

import com.fls.system.model.request.BaseBillRequest;
import com.fls.system.model.response.ProcessResponse;

/**
 * 审批流程增强接口
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface IApproveAwareProcessor {

    /**
     * 提交流程前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeSubmit(BaseBillRequest billRequest) {}

    /**
     * 提交后置业务动作
     *
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterSubmit(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 审批同意前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeAgree(BaseBillRequest billRequest) {}

    /**
     * 审批同意后置业务操作
     * 
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterAgree(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 审批不同意前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeDisagree(BaseBillRequest billRequest) {}

    /**
     * 审批不同意后置业务操作
     *
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterDisagree(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 驳回发起人前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeRejectToInitiator(BaseBillRequest billRequest) {}

    /**
     * 驳回发起人后置业务操作
     *
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterRejectToInitiator(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 驳回上一级前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeRejectUp(BaseBillRequest billRequest) {}

    /**
     * 驳回上一级后置业务操作
     *
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterRejectUp(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 转签后置业务操作
     *
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterTransfer(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 撤销提交前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeRevoke(BaseBillRequest billRequest) {}

    /**
     * 撤销提交后置业务操作
     *
     * @param billRequest 基础单据请求
     * @param resp 通用流程向英国
     */
    default void afterRevoke(BaseBillRequest billRequest, ProcessResponse resp) {}

    /**
     * 作废前置业务动作
     *
     * @param billRequest 基础单据请求
     */
    default void beforeVoided(BaseBillRequest billRequest) {}
}
