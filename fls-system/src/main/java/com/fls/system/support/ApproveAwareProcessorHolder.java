package com.fls.system.support;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.fls.system.model.request.BaseBillRequest;
import com.fls.system.model.response.ProcessResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 审批扩展点操作holder,如果后续需要引入单ResCode多处理器的实现方式，可以引入责任链扩展
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Slf4j
@Service
public class ApproveAwareProcessorHolder {

    private final Map<String, IApproveAwareProcessor> processorHolder = new HashMap<>(4);
    @Autowired
    private ApplicationContext applicationContext;

    public IApproveAwareProcessor getProcessor(String key) {
        IApproveAwareProcessor processor = processorHolder.get(key);
        // 重新从Spring容器获取代理对象
        return applicationContext.getBean(processor.getClass());
    }

    public void putProcessor(String key, IApproveAwareProcessor processor) {
        processorHolder.put(key, processor);
    }

    /**
     * 执行前置处理方法
     *
     * @param billRequest 单据请求
     * @param processorMethod 处理器方法引用
     */
    public void executePreProcessor(BaseBillRequest billRequest, Consumer<IApproveAwareProcessor> processorMethod) {
        Optional.ofNullable(getProcessor(billRequest.getResCode())).ifPresent(processorMethod);
    }

    /**
     * /** 安全执行前置处理方法
     * 
     * @param billRequest 单据请求
     * @param processorMethod 处理器方法引用
     */
    public void safeExecutePreProcessor(BaseBillRequest billRequest, Consumer<IApproveAwareProcessor> processorMethod) {
        try {
            IApproveAwareProcessor processor = getProcessor(billRequest.getResCode());
            processorMethod.accept(processor);
        } catch (Exception e) {
            // 捕获所有异常，确保不影响主流程
            log.error("执行前置处理器方法异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行后置处理方法
     *
     * @param billRequest 单据请求
     * @param response 流程响应
     * @param processorMethod 处理器方法引用
     */
    public void executePostProcessor(BaseBillRequest billRequest, ProcessResponse response, BiConsumer<IApproveAwareProcessor, ProcessResponse> processorMethod) {
        Optional.ofNullable(getProcessor(billRequest.getResCode())).ifPresent(processor -> processorMethod.accept(processor, response));
    }

    /**
     * 安全执行后置处理方法，后续可考虑提取Context进行两个方法合并
     * 
     * @param billRequest 单据请求
     * @param response 流程响应
     * @param processorMethod 处理器方法引用
     * @return 后置事件处理结果
     */
    public boolean safeExecutePostProcessor(BaseBillRequest billRequest, ProcessResponse response, BiConsumer<IApproveAwareProcessor, ProcessResponse> processorMethod) {
        try {
            IApproveAwareProcessor processor = getProcessor(billRequest.getResCode());
            processorMethod.accept(processor, response);
        } catch (Exception e) {
            // 捕获所有异常，确保不影响主流程
            log.error("执行后置处理器方法异常: {}", e.getMessage(), e);
            return false;
        }
        return true;
    }
}