package com.fls.system.support;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 审批扩展点基础抽象
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public abstract class AbstractApproveAwareProcessor implements IApproveAwareProcessor {

    protected String resCode;

    @Resource
    private ApproveAwareProcessorHolder processorHolder;

    @PostConstruct
    private void init() {
        processorHolder.putProcessor(resCode, this);
    }
}
