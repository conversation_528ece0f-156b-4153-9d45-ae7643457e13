package com.fls.system.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.fls.framework.core.enums.SheetStatusEnum;
import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.security.util.LoginHelper;
import com.fls.system.consts.ErrorMsgConstant;
import com.fls.system.model.ResourceApproveInfo;
import com.fls.system.model.ResourceTableInfo;
import com.fls.system.model.request.ApproveRequest;
import com.fls.system.model.request.BaseBillRequest;
import com.fls.system.model.request.ProcessRequest;
import com.fls.system.model.response.ApproveHistory;
import com.fls.system.model.response.ApproveTask;
import com.fls.system.model.response.ProcessResponse;
import com.fls.system.model.response.ResTransType;
import com.fls.system.model.response.ResourceDetail;
import com.fls.system.service.IProcessService;
import com.fls.system.service.IResourceService;
import com.fls.system.support.ApproveAwareProcessorHolder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;

/**
 * 审批流程通用处理器，Handler定义不做扩展层Service定义
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class ApproveProcessHandler {

    private static final String APPROVAL_TITLE_FORMAT = "%s审批流程";

    private final ApproveAwareProcessorHolder approveAwareProcessorHolder;

    private final IResourceService resourceService;

    private final IProcessService processService;

    /**
     * 提交单据发起流程
     *
     * @param billRequest 单据请求
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void submit(BaseBillRequest billRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(billRequest);
        // 单据对单据状态做定制校验逻辑，不做函数参数是因为可读性不高
        if (ObjectUtil.notEqual(resTableInfo.getStatus(), SheetStatusEnum.SAVE.getCode())) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_SAVE);
        }
        // 2. 执行前置处理
        approveAwareProcessorHolder.executePreProcessor(billRequest, processor -> processor.beforeSubmit(billRequest));

        // 3. 创建并提交流程请求
        ProcessResponse processResponse = initiate(billRequest, resTableInfo, processService::startProcess);

        // 4. 保存审批信息
        boolean approved = SheetStatusEnum.AGREE.getCode().equals(processResponse.getBillStatus());
        saveApprovalInfo(resTableInfo, processResponse, approved);

        // 5 执行审批同意后置处理
        boolean postSuccess = true;
        if (approved) {
            postSuccess = approveAwareProcessorHolder.safeExecutePostProcessor(billRequest, processResponse, (processor, resp) -> processor.afterAgree(billRequest, resp));
        }
    }

    public void retryAfterAgree(BaseBillRequest billRequest) {
        ResourceTableInfo resTableInfo = validateAndGetResource(billRequest);
        if (ObjectUtil.notEqual(resTableInfo.getStatus(), SheetStatusEnum.AGREE.getCode())) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_AGREE);
        }
        ProcessResponse processResponse = new ProcessResponse();
        approveAwareProcessorHolder.safeExecutePostProcessor(billRequest, processResponse, (processor, resp) -> processor.afterAgree(billRequest, resp));
    }

    /**
     * 提交审批同意
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void agree(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        SheetStatusEnum status = SheetStatusEnum.of(resTableInfo.getStatus());
        if (status == null || status.isNotApproval()) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_APPROVE);
        }
        String currentUserId = StrUtil.isNotBlank(approveRequest.getIdUser()) ? approveRequest.getIdUser() : LoginHelper.getLoginUser().getIdUser();
        ProcessRequest processRequest = new ProcessRequest();
        BeanUtil.copyProperties(approveRequest, processRequest);
        processRequest.setUserId(currentUserId);
        approveAwareProcessorHolder.executePreProcessor(approveRequest, processor -> processor.beforeAgree(approveRequest));
        // 3. 执行审批同意操作
        ProcessResponse response = processService.agree(processRequest);
        log.info("Process agree for resource code: {}, response: {}", approveRequest.getResCode(), response);
        boolean isFinal = response.getBillStatus().equals(SheetStatusEnum.AGREE.getCode());
        // 4. 保存审批信息
        saveApprovalInfo(resTableInfo, response, isFinal);

        // 5. 执行后置处理
        boolean postExcSuc = true;
        if (isFinal) {
            postExcSuc = approveAwareProcessorHolder.safeExecutePostProcessor(approveRequest, response, (processor, resp) -> processor.afterAgree(approveRequest, resp));
        }
    }

    /**
     * 提交审批不同意
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void disAgree(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        SheetStatusEnum status = SheetStatusEnum.of(resTableInfo.getStatus());
        if (status == null || status.isNotApproval()) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_APPROVE);
        }

        // 2. 执行前置处理
        approveAwareProcessorHolder.executePreProcessor(approveRequest, processor -> processor.beforeDisagree(approveRequest));

        // 3. 执行审批不同意操作
        ProcessResponse processResponse = operate(approveRequest, processService::disagree);

        // 5. 保存审批信息
        saveApprovalInfo(resTableInfo, processResponse, true);

        // 4. 执行后置处理
        boolean postExcSuc =
                approveAwareProcessorHolder.safeExecutePostProcessor(approveRequest, processResponse, (processor, resp) -> processor.afterDisagree(approveRequest, resp));
    }

    /**
     * 驳回制单人
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void rejectToInitiator(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        if (ObjectUtil.notEqual(resTableInfo.getStatus(), SheetStatusEnum.PROCESS.getCode())) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_APPROVE);
        }

        // 2. 执行前置处理
        approveAwareProcessorHolder.executePreProcessor(approveRequest, processor -> processor.beforeRejectToInitiator(approveRequest));

        // 3. 执行驳回至发起人操作
        ProcessResponse processResponse = operate(approveRequest, processService::rejectToInitiator);

        // 4. 保存审批信息
        saveApprovalInfo(resTableInfo, processResponse, false);

        // 5. 执行后置处理
        boolean postExcSuc =
                approveAwareProcessorHolder.safeExecutePostProcessor(approveRequest, processResponse, (processor, resp) -> processor.afterRejectToInitiator(approveRequest, resp));
    }

    /**
     * 驳回上一级
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void rejectPrevious(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        if (ObjectUtil.notEqual(resTableInfo.getStatus(), SheetStatusEnum.PROCESS.getCode())) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_APPROVE);
        }

        // 2. 执行前置处理
        approveAwareProcessorHolder.executePreProcessor(approveRequest, processor -> processor.beforeRejectUp(approveRequest));

        // 3. 执行驳回至上一级操作
        ProcessResponse processResponse = operate(approveRequest, processService::rejectPrevious);

        // 5. 保存审批信息
        saveApprovalInfo(resTableInfo, processResponse, false);

        // 4. 执行后置处理
        boolean postExcSuc =
                approveAwareProcessorHolder.safeExecutePostProcessor(approveRequest, processResponse, (processor, resp) -> processor.afterRejectUp(approveRequest, resp));
    }

    /**
     * 撤销提交
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void revoke(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        if (ObjectUtil.notEqual(resTableInfo.getStatus(), SheetStatusEnum.SUBMIT.getCode())) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_REVOKE);
        }
        // 2. 执行前置处理
        approveAwareProcessorHolder.executePreProcessor(approveRequest, processor -> processor.beforeRevoke(approveRequest));

        // 3. 执行审批不同意操作
        ProcessResponse processResponse = operate(approveRequest, processService::revoke);

        saveApprovalInfo(resTableInfo, processResponse, false);

        // 4. 执行后置处理
        boolean postExcSuc =
                approveAwareProcessorHolder.safeExecutePostProcessor(approveRequest, processResponse, (processor, resp) -> processor.afterRevoke(approveRequest, resp));
    }

    /**
     * 作废流程
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void voided(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        SheetStatusEnum status = SheetStatusEnum.of(resTableInfo.getStatus());
        if (status == null || status.isNotApproval()) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_VOIDED);
        }
        // 2. 执行前置处理
        approveAwareProcessorHolder.executePreProcessor(approveRequest, processor -> processor.beforeVoided(approveRequest));

        // 3. 执行审批不同意操作
        ProcessResponse processResponse = operate(approveRequest, processService::voided);

        // 4. 保存审批信息
        saveApprovalInfo(resTableInfo, processResponse, false);
    }

    /**
     * 转签
     *
     * @param approveRequest 单据审批请求
     * @throws ServiceException 当资源不存在或流程处理失败时抛出
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void transfer(ApproveRequest approveRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(approveRequest);
        SheetStatusEnum status = SheetStatusEnum.of(resTableInfo.getStatus());
        if (status == null || status.isNotApproval()) {
            throw new ServiceException(ErrorMsgConstant.BILL_STATUS_NOT_APPROVE);
        }
        // 2. 执行审批不同意操作
        ProcessResponse processResponse = operate(approveRequest, processService::transfer);

        // 4. 保存审批信息
        saveApprovalInfo(resTableInfo, processResponse, false);

        // 3. 执行后置处理
        boolean postExcSuc =
                approveAwareProcessorHolder.safeExecutePostProcessor(approveRequest, processResponse, (processor, resp) -> processor.afterTransfer(approveRequest, resp));
    }

    public List<ApproveHistory> history(BaseBillRequest billRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(billRequest);
        String idProcessInstance = resTableInfo.getIdProcessInstance();
        if (ObjectUtil.isEmpty(idProcessInstance)) {
            throw new ServiceException(ErrorMsgConstant.BILL_PROCESS_NOT_EXIST);
        }
        return processService.getProcessHistory(idProcessInstance);
    }

    public List<ApproveTask> getTasks(BaseBillRequest billRequest) {
        // 1. 验证并获取资源信息
        ResourceTableInfo resTableInfo = validateAndGetResource(billRequest);
        String idProcessInstance = resTableInfo.getIdProcessInstance();
        if (ObjectUtil.isEmpty(idProcessInstance)) {
            throw new ServiceException(ErrorMsgConstant.BILL_PROCESS_NOT_EXIST);
        }
        return processService.getTasks(idProcessInstance, billRequest.getResCode());
    }

    /**
     * 验证并获取资源信息,两部分校验：1.资源是否注册;2.资源实例是否存在
     *
     * @param billRequest 单据请求
     * @return 资源表信息
     */
    private ResourceTableInfo validateAndGetResource(BaseBillRequest billRequest) {
        ResourceDetail detail = resourceService.queryResTableInfo(billRequest.getIdRes());
        Assert.notNull(detail, () -> new ServiceException(ErrorMsgConstant.RESOURCE_NOT_EXIST));
        billRequest.setResCode(detail.getCode());
        if (ObjectUtil.isNotEmpty(billRequest.getIdTransType())) {
            List<ResTransType> transList = detail.getTransList();
            for (ResTransType transType : transList) {
                if (billRequest.getIdTransType().equals(transType.getIdTransType())) {
                    billRequest.setTransCode(transType.getCode());
                    break;
                }
            }
        }
        ResourceApproveInfo resourceApproveInfo;
        try {
            // 校验资源实例记录是否存在
            resourceApproveInfo = resourceService.queryResApproveInfo(detail.getTblName(), detail.getTblIdName(), billRequest.getIdObj());
            Assert.notNull(resourceApproveInfo, () -> new ServiceException(ErrorMsgConstant.RESOURCE_INSTANCE_NOT_EXIST));
        }
        catch (BadSqlGrammarException e) {
            log.error("resource bill query approve info sql execute failed", e);
            throw new ServiceException("单据资源配置异常，请联系管理员");
        }
        return ResourceTableInfo.from(detail, billRequest, resourceApproveInfo);
    }

    /**
     * 发起流程操作，并对操作函数做了参数抽象
     *
     * @param billRequest  单据请求
     * @param resourceInfo 资源表信息
     * @param procFunc     流程执行函数
     * @return 流程响应
     */
    private ProcessResponse initiate(BaseBillRequest billRequest, ResourceTableInfo resourceInfo, Function<ProcessRequest, ProcessResponse> procFunc) {
        String currentUserId = StrUtil.isNotBlank(billRequest.getIdUser()) ? billRequest.getIdUser() : LoginHelper.getLoginUser().getIdUser();
        String title = String.format(APPROVAL_TITLE_FORMAT, resourceInfo.getResName());
        ProcessRequest processRequest = ProcessRequest.from(billRequest, title, currentUserId, true);
        // 单针对重新发起的逻辑做补充字段
        if (ObjectUtil.isNotEmpty(resourceInfo.getIdProcessInstance())) {
            processRequest.setProcessInstanceId(resourceInfo.getIdProcessInstance());
        }
        // 发起审批流程
        ProcessResponse response = procFunc.apply(processRequest);
        log.info("Process start for resource code: {}, response: {}", billRequest.getResCode(), response);
        return response;
    }

    /**
     * 执行流程操作，并对操作函数做了参数抽象，这里拆分两个方法因为发起和审批的操作函数参数不同
     *
     * @param approveReq 申请请求参数
     * @param procFunc   流程执行函数
     * @return 通用流程响应
     */
    private ProcessResponse operate(ApproveRequest approveReq, Function<ProcessRequest, ProcessResponse> procFunc) {
        String currentUserId = StrUtil.isNotBlank(approveReq.getIdUser()) ? approveReq.getIdUser() : LoginHelper.getLoginUser().getIdUser();
        ProcessRequest processRequest = new ProcessRequest();
        BeanUtil.copyProperties(approveReq, processRequest);
        processRequest.setUserId(currentUserId);
        // 执行流程操作
        ProcessResponse response = procFunc.apply(processRequest);
        log.info("Process operate for resource code: {}, response: {}", approveReq.getResCode(), response);
        return response;
    }

    /**
     * 按照资源表信息保存对应表审批字段
     *
     * @param resourceInfo    资源表信息
     * @param processResponse 通用流程响应
     * @param auditFlag       是否写入审批人、审批时间字段
     */
    private void saveApprovalInfo(ResourceTableInfo resourceInfo, ProcessResponse processResponse, boolean auditFlag) {
        ResourceApproveInfo approveInfo = new ResourceApproveInfo(resourceInfo);
        approveInfo.setStatus(processResponse.getBillStatus());
        approveInfo.setIdProcessInstance(processResponse.getProcessInstanceId());
        if (auditFlag) {
            String currentUserId = resourceInfo.getIdUser();
            approveInfo.setAuditor(currentUserId);
            approveInfo.setAuditTime(LocalDateTime.now());
        }
        try {
            // 校验资源实例记录是否存在
            resourceService.saveApproveInfo(approveInfo);
        }
        catch (BadSqlGrammarException e) {
            log.error("resource bill save approve info sql execute failed", e);
            throw new ServiceException("单据资源保存异常，请联系管理员");
        }
    }
}