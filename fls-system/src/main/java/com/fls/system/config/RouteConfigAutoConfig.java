package com.fls.system.config;

import com.fls.system.interceptor.ComponentRouteInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 路由自动配置
 *
 * <AUTHOR>
 * @date 2025/3/28 11:02
 */
@Configuration
public class RouteConfigAutoConfig {

    @Bean
    public ComponentRouteInterceptor routeInterceptor() {
        return new ComponentRouteInterceptor();
    }


    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(routeInterceptor())
                        .addPathPatterns("/**"); // 拦截所有路径
            }
        };
    }
}
