package com.fls.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.fls.system.model.ResourceApproveInfo;

/**
 * 与审批相关的数据库操作
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Mapper
public interface ApproveMapper {
    /**
     * 更新资源表对应的审批信息
     *
     * @param approveInfo 资源审批信息
     */
    void updateResAudit(@Param(value = "approve") ResourceApproveInfo approveInfo);

    /**
     * 查询资源审批流程信息
     *
     * @param table 表名
     * @param idColumn 主键名
     * @param id 主键id
     * @return 审批信息
     */
    ResourceApproveInfo queryResAudit(@Param("table") String table, @Param("idColumn") String idColumn,
        @Param("id") String id);
}