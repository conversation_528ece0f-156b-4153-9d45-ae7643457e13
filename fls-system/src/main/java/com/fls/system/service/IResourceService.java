package com.fls.system.service;

import com.fls.system.model.ResourceApproveInfo;
import com.fls.system.model.ResourceCodeInfo;
import com.fls.system.model.response.ResourceDetail;

/**
 * 资源管理服务
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface IResourceService {
    /**
     * 通过资源编码查询资源信息（带流水号）
     * 
     * @param code 资源编码
     * @return 资源信息
     */
    ResourceCodeInfo queryResourceInfo(String code);

    /**
     * 更新指定资源的流水编号，因为跨服务不能通过本地数据库操作
     * 
     * @param code 资源编码
     * @param flowNum 资源流水单号
     */
    void updateResourceFlow(String code, Integer flowNum);

    /**
     * 保存资源审批流程信息，通过表名和主键id列做本地表存储，不走服务总线，因为跨服务不能通过本地数据库操作
     *
     * @param approveInfo 审批信息
     */
    void saveApproveInfo(ResourceApproveInfo approveInfo);

    /**
     * 通过资源编码查询资源表信息（资源对应表，对应表id字段）
     *
     * @param idRes 资源编码
     * @return 资源表信息
     */
    ResourceDetail queryResTableInfo(String idRes);

    /**
     * 通过资源编码信息审批流程信息
     * 
     * @param table 资源对应表名称
     * @param idColumn 资源对应表主键名称
     * @param id 资源对应表id
     * @return 资源审批信息
     */
    ResourceApproveInfo queryResApproveInfo(String table, String idColumn, String id);
}
