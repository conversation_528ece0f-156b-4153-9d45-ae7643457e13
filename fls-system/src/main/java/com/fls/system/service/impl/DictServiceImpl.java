package com.fls.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.framework.core.service.DictService;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.consts.ApiConstant;
import com.fls.system.model.DictValueInfo;

import lombok.AllArgsConstructor;

/**
 * DictServiceImpl
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Service
@AllArgsConstructor
public class DictServiceImpl implements DictService {

    private final ApiClientFacade apiClientFacade;

    @Override
    public String getDictValue(String dictType, String dictCode) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("dictType", dictType).put("dictCode", dictCode).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_DICT_VALUE_QUERY, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        List<DictValueInfo> list = BeanUtil.copyToList((List<?>)response.getData(), DictValueInfo.class);
        return list.stream().filter(item -> dictCode.equals(item.getDictCode())).map(DictValueInfo::getDictName).findFirst().orElse(StrUtil.EMPTY);
    }
}
