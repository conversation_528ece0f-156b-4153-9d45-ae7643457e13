package com.fls.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.fls.framework.core.enums.SheetStatusEnum;
import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.consts.ApproveConstant;
import com.fls.system.enums.ProcessStatusEnum;
import com.fls.system.model.ProcessTaskInfo;
import com.fls.system.model.request.ProcessRequest;
import com.fls.system.model.response.ApproveHistory;
import com.fls.system.model.response.ApproveNextTask;
import com.fls.system.model.response.ApproveTask;
import com.fls.system.model.response.ProcessResponse;
import com.fls.system.service.IProcessService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用流程操作服务
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Slf4j
@Service
@AllArgsConstructor
public class ProcessServiceImpl implements IProcessService {

    private final ApiClientFacade apiClientFacade;

    @Override
    public ProcessResponse startProcess(ProcessRequest req) {
        String processInstanceId = req.getProcessInstanceId();
        // 根据是否有processInstanceId决定调用方式
        ProcessResponse response = ObjectUtil.isNotEmpty(processInstanceId) ? getProcessResponse(req, processInstanceId) : invoke(ApproveConstant.API_PROCESS_START, req);

        // 如果是提交节点则自动审核
        if (ApproveConstant.SUBMIT_NODE_NAME.equals(response.getTask().getStr("name"))) {
            response = getProcessResponse(req, response.getProcessInstanceId());
        }
        int status = response.getStatus();
        // 以流程响应的状态结果，转换为通用的业务单据状态
        String billStatus = status == ProcessStatusEnum.INGRESS.getCode() ? SheetStatusEnum.SUBMIT.getCode() : SheetStatusEnum.AGREE.getCode();
        response.setBillStatus(billStatus);
        return response;
    }

    /**
     * 发起申请的自动审核通过方法
     *
     * @param req 流程通用请求
     * @param idProcessInstanceId 流程实例id
     * @return ProcessResponse 流程通用响应
     */
    private ProcessResponse getProcessResponse(ProcessRequest req, String idProcessInstanceId) {
        ProcessRequest nxtReq = new ProcessRequest();
        BeanUtil.copyProperties(req, nxtReq);
        nxtReq.setResult(ApproveConstant.APPROVE_AGREE_RESULT);
        nxtReq.setProcessInstanceId(idProcessInstanceId);
        nxtReq.setRemarks("自动审核通过");
        return approve(nxtReq, 1, SheetStatusEnum.AGREE);
    }

    @Override
    public ProcessResponse disagree(ProcessRequest req) {
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_DISAGREE, req);
        int status = response.getStatus();
        // 以流程响应的状态结果，转换为通用的业务单据状态
        String billStatus = status == ProcessStatusEnum.INGRESS.getCode() ? SheetStatusEnum.PROCESS.getCode() : SheetStatusEnum.DISAGREE.getCode();
        response.setBillStatus(billStatus);
        return response;
    }

    @Override
    public ProcessResponse approve(ProcessRequest req, int result, SheetStatusEnum finalStatus) {
        req.setResult(result);
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_APPROVE, req);
        int status = response.getStatus();
        String billStatus = status == ProcessStatusEnum.INGRESS.getCode() ? SheetStatusEnum.PROCESS.getCode() : finalStatus.getCode();
        response.setBillStatus(billStatus);
        return response;
    }

    @Override
    public ProcessResponse rejectToInitiator(ProcessRequest req) {
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_REJECT_APPLICANT, req);
        String billStatus = SheetStatusEnum.SAVE.getCode();
        response.setBillStatus(billStatus);
        return response;
    }

    @Override
    public ProcessResponse rejectPrevious(ProcessRequest req) {
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_REJECT_PREVIOUS, req);
        String billStatus;
        // 如果驳回的节点是提交申请节点，则更新单据状态为新提交状态
        if (ApproveConstant.SUBMIT_NODE_NAME.equals(response.getTask().getStr("name"))) {
            billStatus = SheetStatusEnum.SUBMIT.getCode();
        } else {
            billStatus = SheetStatusEnum.PROCESS.getCode();
        }
        response.setBillStatus(billStatus);
        return response;
    }

    @Override
    public ProcessResponse transfer(ProcessRequest req) {
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_REJECT_TRANSFER, req);
        // 响应为void，单独做处理逻辑
        ProcessResponse returnResp = new ProcessResponse();
        returnResp.setBillStatus(SheetStatusEnum.PROCESS.getCode());
        returnResp.setProcessInstanceId(req.getProcessInstanceId());
        return returnResp;
    }

    @Override
    public ProcessResponse revoke(ProcessRequest req) {
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_REVOKE, req);
        String billStatus = SheetStatusEnum.SAVE.getCode();
        response.setBillStatus(billStatus);
        return response;
    }

    @Override
    public ProcessResponse voided(ProcessRequest req) {
        ProcessResponse response = invoke(ApproveConstant.API_PROCESS_VOIDED, req);
        String billStatus = SheetStatusEnum.VOIDED.getCode();
        response.setBillStatus(billStatus);
        return response;
    }

    public ProcessResponse invoke(String path, ProcessRequest req) {
        Map<String, Object> params = BeanUtil.beanToMap(req);
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), path, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        return BeanUtil.toBean(response.getData(), ProcessResponse.class);
    }

    @Override
    public List<ApproveHistory> getProcessHistory(String processInstanceId) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>(1)).put("processInstanceId", processInstanceId).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApproveConstant.API_PROCESS_HISTORY, params);
        log.info("Process history for process instance id: {}, response: {}", processInstanceId, result);
        List<ApproveHistory> hisResp = new ArrayList<>(8);
        // 这里因为服务层的响应定义涉及很多Flowable底层的Bean定义，改为通过Json获取
        JSONObject response = JSONUtil.toBean(result, JSONObject.class);
        JSONObject data = response.getJSONObject("data");
        if (ObjectUtil.isNotNull(data)) {
            JSONArray historyFlowList = data.getJSONArray("historyFlowList");
            for (Object flow : historyFlowList) {
                JSONObject flowJson = JSONUtil.parseObj(flow);
                ApproveHistory approveHistory = new ApproveHistory();
                approveHistory.setNode(flowJson.getJSONObject("histIns").getStr("activityName"));
                approveHistory.setAssignee(flowJson.getStr("assigneeName"));
                approveHistory.setBeginTime(flowJson.getJSONObject("histIns").getStr("startTime"));
                approveHistory.setEndTime(flowJson.getJSONObject("histIns").getStr("endTime"));
                approveHistory.setStatus(flowJson.getJSONObject("comment").getStr("status"));
                approveHistory.setMessage(flowJson.getJSONObject("comment").getStr("message"));
                approveHistory.setTitle(flowJson.getJSONObject("comment").getStr("message"));
                approveHistory.setDuration(flowJson.getStr("durationTime"));
                hisResp.add(approveHistory);
            }
        }
        return hisResp;
    }

    @Override
    public List<ApproveTask> getTasks(String processInstanceId, String resCode) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>(1)).put("processInstanceId", processInstanceId).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApproveConstant.API_PROCESS_TASKS, params);
        log.info("Process todo tasks for process instance id: {}, response: {}", processInstanceId, result);
        List<ApproveTask> apvTasksResp = new ArrayList<>(8);
        // 这里因为服务层的响应定义涉及很多Flowable底层的Bean定义，改为通过Json获取
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        List<ProcessTaskInfo> taskInfos = JSONUtil.toList(JSONUtil.parseArray(response.getData()), ProcessTaskInfo.class);
        if (ObjectUtil.isNotEmpty(taskInfos)) {
            for (ProcessTaskInfo taskInfo : taskInfos) {
                ApproveTask apvTask = new ApproveTask();
                apvTask.setIdTask(taskInfo.getId());
                apvTask.setAuditor(taskInfo.getAssignee());
                apvTask.setAuditorName(taskInfo.getAssigneeName());
                apvTask.setBeginTime(taskInfo.getCreateTime().toString());
                apvTask.setNodeName(taskInfo.getName());
                apvTask.setProcName(taskInfo.getProcessName());
                apvTask.setResourceCode(resCode);
                apvTasksResp.add(apvTask);
            }
        }
        return apvTasksResp;
    }

    @Override
    public ApproveNextTask getNextTask(ProcessRequest req) {
        String processInstanceId = req.getProcessInstanceId();
        String userId = req.getUserId();
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>(2)).put("processInstanceId", processInstanceId).put("userId", userId).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApproveConstant.API_PROCESS_NEXT_TASK, params);
        log.info("Process next tasks for process instance id: {}, response: {}", processInstanceId, result);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        // 处理空的JSONObject情况
        JSONObject jsonData = JSONUtil.parseObj(response.getData());
        if (jsonData.isEmpty() || ObjectUtil.isNull(response.getData())) {
            return null;
        }
        return BeanUtil.toBean(response.getData(), ApproveNextTask.class);
    }
}
