package com.fls.system.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.fls.framework.core.config.AppConfig;
import com.fls.framework.core.config.ProjectConfig;
import com.fls.framework.core.exception.AuthException;
import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.exception.enums.AuthExceptionEnum;
import com.fls.framework.core.pojo.login.LoginUser;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.framework.core.util.AddressUtils;
import com.fls.framework.core.util.EncryptUtils;
import com.fls.framework.core.util.HttpServletUtil;
import com.fls.framework.core.util.IpAddressUtil;
import com.fls.framework.core.util.UaUtil;
import com.fls.framework.security.enums.UserType;
import com.fls.framework.security.service.LoginCheckService;
import com.fls.framework.security.util.LoginHelper;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.consts.ApiConstant;
import com.fls.system.service.IAuthService;
import com.fls.system.service.ILoginService;

import lombok.extern.slf4j.Slf4j;

/**
 * UserServiceImpl
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Service
@Slf4j
public class LoginServiceImpl implements ILoginService, LoginCheckService {

    @Resource
    private ApiClientFacade apiClientFacade;

    @Resource
    private AppConfig appConfig;

    @Value("${sso.check-ticket-url}")
    private String checkTicketUrl;

    @Resource
    private IAuthService authService;

    @Resource
    private ProjectConfig projectConfig;

    @Override
    public void checkLogin() {
        String token = StpUtil.getTokenValue();
        if (ObjectUtil.isEmpty(token)) {
            throw new AuthException(AuthExceptionEnum.REQUEST_TOKEN_EMPTY);
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        long time = System.currentTimeMillis();
        log.info("==============》request-no = {} , userId = {}  , username = {} ", time, loginUser.getIdUser(), loginUser.getUsername());
    }

    @Override
    public LoginUser login(String username, String password) {
        password = EncryptUtils.decryptByAscii(password);
        String appCode = StrUtil.equals(SaHolder.getRequest().getHeader("Client-Type"), "web") ? projectConfig.getCode() : projectConfig.getAppletCode();
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("username", username).put("password", password).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_USER_LOGIN, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        LoginUser loginUser = BeanUtil.toBean(response.getData(), LoginUser.class);
        fillUserEx(loginUser);
        LoginHelper.login(loginUser);
        // 向服务层发起缓存token的逻辑
        authService.setUserAuth(appCode, loginUser);
        // 缓存用户权限，这里暂时注释掉，因为登录的时候已经缓存了
        // authService.cacheUserProjectAuth(appCode, loginUser.getIdUser());
        return loginUser;
    }

    @Override
    public LoginUser loginWithTicket(@NotEmpty String ticket) {
        if (ObjectUtils.isEmpty(ticket)) {
            throw new RuntimeException("ticket不能为空");
        }
        String appCode = StrUtil.equals(SaHolder.getRequest().getHeader("Client-Type"), "web") ? projectConfig.getCode() : projectConfig.getAppletCode();
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("ticket", ticket).build();
        String result = HttpUtil.get(checkTicketUrl, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        JSONObject obj = JSONUtil.parseObj(response.getData());
        LoginUser loginUser = BeanUtil.toBean(obj.get("loginUser"), LoginUser.class);
        String token = obj.getStr("token");
        if (ObjectUtil.isNull(loginUser) || StrUtil.isBlank(token)) {
            throw new ServiceException("票据认证失败");
        }
        fillUserEx(loginUser);
        LoginHelper.loginByToken(loginUser, token);
        // 向服务层发起缓存token的逻辑
        authService.setUserAuth(appCode, loginUser);
        return loginUser;
    }

    /**
     * 补充一部分扩展信息
     */
    private void fillUserEx(LoginUser loginUser) {
        String requestPath = SaHolder.getRequest().getRequestPath();
        // 根据url前缀选择不同的token校验
        if (requestPath.startsWith(appConfig.getAppMinopStartUrl())) {
            loginUser.setUserType(UserType.APP_USER.getUserType());
        } else {
            loginUser.setUserType(UserType.SYS_USER.getUserType());
        }
        // 封装登录信息
        HttpServletRequest request = HttpServletUtil.getRequest();
        if (ObjectUtil.isNotNull(request)) {
            String ip = IpAddressUtil.getIp(request);
            loginUser.setIpAddr(ip);
            loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
            loginUser.setBrowser(UaUtil.getBrowser(request));
            loginUser.setOs(UaUtil.getOs(request));
            loginUser.setLoginTime(new Date());
            loginUser.setExpireTime(DateUtil.offsetSecond(new Date(), (int)SaManager.getConfig().getTimeout()));
        }
    }

    @Override
    public void logout() {
        if (!StpUtil.isLogin()) {
            throw new ServiceException("账户未登录，请先登录！");
        }
        String token = StpUtil.getTokenValue();
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("token", token).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_USER_LOGOUT, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        StpUtil.logoutByTokenValue(token);
    }
}
