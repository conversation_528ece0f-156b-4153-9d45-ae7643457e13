package com.fls.system.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;

import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.consts.ApiConstant;
import com.fls.system.mapper.ApproveMapper;
import com.fls.system.model.ResourceApproveInfo;
import com.fls.system.model.ResourceCodeInfo;
import com.fls.system.model.response.ResourceDetail;
import com.fls.system.service.IResourceService;

import lombok.extern.slf4j.Slf4j;

/**
 * 资源管理服务实现
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Service
@Slf4j
public class ResourceServiceImpl implements IResourceService {

    @Resource
    private ApiClientFacade apiClientFacade;

    @Resource
    private ApproveMapper approveMapper;

    @Override
    public ResourceCodeInfo queryResourceInfo(String code) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("code", code).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_RESOURCE_CODE, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        return BeanUtil.toBean(response.getData(), ResourceCodeInfo.class);
    }

    @Override
    public void updateResourceFlow(String code, Integer flowNum) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("code", code).put("flowNum", flowNum).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_RESOURCE_CODE_UPDATE, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
    }

    @Override
    public void saveApproveInfo(ResourceApproveInfo approveInfo) {
        approveMapper.updateResAudit(approveInfo);
    }

    @Override
    public ResourceDetail queryResTableInfo(String idRes) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("id", idRes).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_RESOURCE_DETAIL, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        return BeanUtil.toBean(response.getData(), ResourceDetail.class);
    }

    @Override
    public ResourceApproveInfo queryResApproveInfo(String table, String idColumn, String id) {
        return approveMapper.queryResAudit(table, idColumn, id);
    }
}
