package com.fls.system.service;

import com.fls.framework.core.pojo.login.LoginUser;

/**
 * 登录服务
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface ILoginService {
    /**
     * 通过用户名和密码登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录用户信息
     */
    LoginUser login(String username, String password);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 通过ticket进行单点登录
     *
     * @param ticket 票据凭证
     * @return 登录用户信息
     */
    LoginUser loginWithTicket(String ticket);
}
