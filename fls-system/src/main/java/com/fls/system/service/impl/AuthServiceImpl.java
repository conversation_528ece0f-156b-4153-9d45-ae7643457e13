package com.fls.system.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.fls.framework.core.config.ProjectConfig;
import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.pojo.login.LoginUser;
import com.fls.framework.core.pojo.response.ResponseData;
import com.fls.framework.security.util.LoginHelper;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.consts.ApiConstant;
import com.fls.system.consts.ResConstant;
import com.fls.system.service.IAuthService;

/**
 * AuthServiceImpl
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Service
public class AuthServiceImpl implements IAuthService {
    @Resource
    private ApiClientFacade apiClientFacade;

    @Resource
    private ProjectConfig projectConfig;

    @Override
    public void cacheUserProjectAuth(String token) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("token", token).build();
        String invoke = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_SET_AUTH, params);
        ResponseData response = JSONUtil.toBean(invoke, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
    }

    @Override
    public void setUserAuth(String appCode, LoginUser loginUser) {
        String userId = loginUser.getIdUser();
        Map<String, Object> cacheParams = MapUtil.builder(new HashMap<String, Object>()).put("userId", userId).put("appcode", appCode).put("token", StpUtil.getTokenValue()).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_USER_CACHE_LOGIN, cacheParams);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        cacheUserScope(loginUser);
    }

    @Override
    public void cacheUserScope(LoginUser loginUser) {
        String token = StpUtil.getTokenValue();
        Map<String, Object> params = MapUtil.of("token", token);
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_GET_AUTH, params);
        ResponseData response = JSONUtil.toBean(result, ResponseData.class);
        if (ResponseData.isNotSuccess(response)) {
            throw new ServiceException(response.getMsg());
        }
        Map<String, Object> auths = JSONUtil.parseObj(response.getData());
        Map<String, Set<String>> orgScope = new HashMap<>(32);
        Map<String, Set<String>> unitScope = new HashMap<>(32);
        // 遍历每个数组进行组合拼接
        for (Map.Entry<String, Object> entry : auths.entrySet()) {
            JSONObject roleAuths = JSONUtil.parseObj(entry.getValue());
            JSONArray menus = roleAuths.getJSONArray("menu");
            JSONArray orgSet = roleAuths.getJSONArray("org");
            JSONArray dataSet = roleAuths.getJSONArray("data");
            Set<String> hrefs = menus.stream().map(item -> JSONUtil.parseObj(item).getStr("href")).collect(Collectors.toSet());
            Set<String> orgIds = orgSet.stream().map(item -> JSONUtil.parseObj(item).getStr("id_org")).collect(Collectors.toSet());
            Set<String> unitIds = orgSet.stream().map(item -> JSONUtil.parseObj(item).getStr("id_bizunit")).collect(Collectors.toSet());
            // 档案经营主体过滤
            Set<String> unitDocs =
                dataSet.stream().filter(item -> StrUtil.equals(JSONUtil.parseObj(item).getStr("id_resource"), ResConstant.BIZ_UNIT_RES_ID)).map(item -> JSONUtil.parseObj(item).getStr("id_resdoc")).collect(Collectors.toSet());
            unitIds.addAll(unitDocs);
            for (String href : hrefs) {
                orgScope.computeIfAbsent(href, k -> new HashSet<>()).addAll(orgIds);
                unitScope.computeIfAbsent(href, k -> new HashSet<>()).addAll(unitIds);
            }
        }
        loginUser.setOrgDataScopes(orgScope);
        loginUser.setUnitDataScopes(unitScope);
        LoginHelper.setLoginUser(loginUser);
        // 将用户的组织权限列表缓存至缓存中，但返回的时候排除掉该字段，前端不使用该字段
        loginUser.setUnitDataScopes(null);
        loginUser.setOrgDataScopes(null);
    }

    @Override
    public ResponseData getUserAuth() {
        return getResourceByToken(ApiConstant.API_UPMS_GET_AUTH, null, null);
    }

    @Override
    public ResponseData getAuthOrg(String href) {
        return getResourceByToken(ApiConstant.API_UPMS_GET_ORG_AUTH, href, null);
    }

    @Override
    public ResponseData getAuthPost(String href) {
        return getResourceByToken(ApiConstant.API_UPMS_GET_POST_AUTH, href, null);
    }

    @Override
    public ResponseData getAuthDept(String href) {
        return getResourceByToken(ApiConstant.API_UPMS_GET_DEPT_AUTH, href, null);
    }

    @Override
    public ResponseData getAuthDoc(String href, String resourceId) {
        return getResourceByToken(ApiConstant.API_UPMS_GET_RESOURCE_AUTH, href, resourceId);
    }

    @Override
    public ResponseData getAuthMenu() {
        String token = StpUtil.getTokenValue();
        String appcode = StrUtil.equals(SaHolder.getRequest().getHeader("Client-Type"), "web") ? projectConfig.getCode() : projectConfig.getAppletCode();
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("token", token).put("projectName", appcode).build();
        String invoke = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_GET_MENU, params);
        return JSONUtil.toBean(invoke, ResponseData.class);
    }

    private ResponseData getResourceByToken(String uri, String href, String resourceId) {
        // 前面check逻辑确定token是登陆过的
        String token = StpUtil.getTokenValue();
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("token", token).put("href", href).put("resourceId", resourceId).build();
        String invoke = apiClientFacade.invoke(HttpMethod.POST.name(), uri, params);
        return JSONUtil.toBean(invoke, ResponseData.class);
    }

    @Override
    public ResponseData getProjectAuthMenu(String projectCode) {
        String userId = LoginHelper.getLoginUser().getIdUser();
        if (StrUtil.isEmpty(userId)) {
            throw new ServiceException("用户未登录，请重新登录");
        }
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("userId", userId).put("appcode", projectCode).build();
        String invoke = apiClientFacade.invoke(HttpMethod.POST.name(), ApiConstant.API_UPMS_GET_PROJECT_MENU, params);
        return JSONUtil.toBean(invoke, ResponseData.class);
    }
}
