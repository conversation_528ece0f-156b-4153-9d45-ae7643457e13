package com.fls.system.service;

import com.fls.framework.core.pojo.login.LoginUser;
import com.fls.framework.core.pojo.response.ResponseData;

public interface IAuthService {
    void cacheUserProjectAuth(String token);

    void setUserAuth(String appCode, LoginUser loginUser);

    void cacheUserScope(LoginUser loginUser);

    ResponseData getUserAuth();

    ResponseData getAuthOrg(String href);

    ResponseData getAuthPost(String href);

    ResponseData getAuthDept(String href);

    ResponseData getAuthDoc(String href, String resourceId);

    ResponseData getAuthMenu();

    Object getProjectAuthMenu(String projectCode);
}
