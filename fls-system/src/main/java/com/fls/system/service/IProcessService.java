package com.fls.system.service;

import java.util.List;

import com.fls.framework.core.enums.SheetStatusEnum;
import com.fls.system.consts.ApproveConstant;
import com.fls.system.model.request.ProcessRequest;
import com.fls.system.model.response.ApproveHistory;
import com.fls.system.model.response.ApproveNextTask;
import com.fls.system.model.response.ApproveTask;
import com.fls.system.model.response.ProcessResponse;

/**
 * 通用流程服务
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface IProcessService {

    /**
     * 发起流程
     *
     * @param req 流程请求
     * @return 流程操作响应
     */
    ProcessResponse startProcess(ProcessRequest req);

    /**
     * 审批同意流程操作
     *
     * @param req 通用流程请求
     * @return 通用流程响应
     */
    default ProcessResponse agree(ProcessRequest req) {
        return approve(req, ApproveConstant.APPROVE_AGREE_RESULT, SheetStatusEnum.AGREE);
    }

    /**
     * 审批不同意流程操作
     *
     * @param req 通用流程请求
     * @return 通用流程响应
     */
    ProcessResponse disagree(ProcessRequest req);

    /**
     * 审批流程操作
     *
     * @param req 通用流程请求
     * @param result 审批结果
     * @param finalStatus 审批状态
     * @return 通用流程响应
     */
    ProcessResponse approve(ProcessRequest req, int result, SheetStatusEnum finalStatus);

    /**
     * 驳回至制单人
     *
     * @param req 流程请求
     * @return 流程操作响应
     */
    ProcessResponse rejectToInitiator(ProcessRequest req);

    /**
     * 驳回至上一级
     *
     * @param req 流程请求
     * @return 流程操作响应
     */
    ProcessResponse rejectPrevious(ProcessRequest req);

    /**
     * 转办
     *
     * @param req 流程请求
     * @return 流程操作响应
     */
    ProcessResponse transfer(ProcessRequest req);

    /**
     * 撤销流程
     *
     * @param req 流程请求
     * @return 流程操作响应
     */
    ProcessResponse revoke(ProcessRequest req);

    /**
     * 作废
     *
     * @param req 流程请求
     * @return 流程操作响应
     */
    ProcessResponse voided(ProcessRequest req);

    /**
     * 获取流程历史任务
     *
     * @param processInstanceId 流程实例id
     * @return 流程历史列表
     */
    List<ApproveHistory> getProcessHistory(String processInstanceId);

    /**
     * 获取待审任务列表
     *
     * @param processInstanceId 流程实例id
     * @param resCode 资源编码
     * @return 待审任务列表
     */
    List<ApproveTask> getTasks(String processInstanceId, String resCode);

    /**
     * 获取下一个审批节点信息
     *
     * @param req 流程请求
     * @return 下一个审批节点信息
     */
    ApproveNextTask getNextTask(ProcessRequest req);

}
