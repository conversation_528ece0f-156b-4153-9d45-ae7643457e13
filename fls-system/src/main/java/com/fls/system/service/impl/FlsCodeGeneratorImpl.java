package com.fls.system.service.impl;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.fls.framework.core.config.ProjectConfig;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.fls.system.model.ResourceCodeInfo;
import com.fls.system.service.FlsCodeGenerator;
import com.fls.system.service.IResourceService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * CodeGeneratorImpl
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Slf4j
@Component
@AllArgsConstructor
public class FlsCodeGeneratorImpl implements FlsCodeGenerator {

    public static final String CODE_CATALOG = "fls:code:cache";

    private final RedissonClient redissonClient;

    private final ProjectConfig projectConfig;

    private final ApiClientFacade apiClientFacade;

    private final IResourceService resourceService;

    /**
     * 左补位
     *
     * @param str 字符串
     * @param length 总位数
     * @return Result 补位后的字符串
     */
    public static String fillLeftZero(String str, Integer length) {
        int strLen = str.length();
        if (strLen < length) {
            while (strLen < length) {
                str = "0" + str;
                strLen++;
            }
        }
        return str;
    }

    @Override
    public String genBillCode(String code) {
        ResourceCodeInfo resourceCodeInfo = resourceService.queryResourceInfo(code);
        Integer codeLen = resourceCodeInfo.getLenCode() - resourceCodeInfo.getPrefix().length();
        String prefix = resourceCodeInfo.getPrefix();
        String busKey = projectConfig.getCode() + ":" + prefix;
        RLock rLock = redissonClient.getLock(code);
        try {
            rLock.tryLock();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(CODE_CATALOG + "::" + busKey);
            long num = atomicLong.incrementAndGet();
            String coverStr = fillLeftZero(String.valueOf(num), codeLen);
            resourceService.updateResourceFlow(code, (int)num);
            // 拼接
            return prefix + coverStr;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            rLock.unlock();
        }
    }

    @Override
    public String genIncrementCode(String prefix, int length) {
        RLock rLock = redissonClient.getLock(prefix);
        try {
            rLock.tryLock();
            String busKey = projectConfig.getCode() + ":" + prefix;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(CODE_CATALOG + "::" + busKey);
            long num = atomicLong.incrementAndGet();
            // 补位
            String coverStr = fillLeftZero(String.valueOf(num), length);
            // 拼接
            return prefix + coverStr;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            rLock.unlock();
        }
    }
}
