package com.fls.system.util;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/27 10:17
 */
public class HttpHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpHelper.class);

    /**
     * 从request中获取body内容转换为string
     *
     * @param request
     * @return
     */
    public static String getBodyString(ServletRequest request) {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = null;
        try (InputStream inputStream = request.getInputStream()) {
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = "";
            while ((line = reader.readLine()) != null) {
                sb.append(line.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
            LOGGER.warn("getBodyString出现问题！");
        }
        finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.fillInStackTrace();
                }
            }
        }
        return sb.toString();
    }


    /**
     * 从request中获取body内容转换为map
     *
     * @param request
     * @return
     */
    public static Map<String, Object> getBodyMap(ServletRequest request) {
        String bodyString = ServletUtil.getBody(request);
        return StrUtil.isNotBlank(bodyString) ? JSONUtil.toBean(bodyString, new TypeReference<Map<String, Object>>() {
        }, true) : new HashMap<>();
    }
}
