package com.fls.system.model.response;

import java.util.List;

import lombok.Data;

import cn.hutool.json.JSONObject;

/**
 * 基础审批流程响应
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
public class ProcessResponse {
    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 当前审批人信息
     */
    private List<String> userIds;

    /**
     * 当前流程状态 1-执行中 2-已完成
     */
    private int status;

    /**
     * 单据状态，冗余字段，做状态转换时使用
     */
    private String billStatus;

    /**
     * 当前审批节点信息
     */
    private JSONObject task;
}