package com.fls.system.model;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class ComponentRouteContext {

    private HttpServletRequest request;

    private HttpServletResponse response;

    private String requestUri;

    private String method = "POST";

    private Map<String, Object> bodyMap;

    public ComponentRouteContext(HttpServletRequest request, HttpServletResponse response, String requestUri) {
        this.request = request;
        this.response = response;
        this.requestUri = requestUri;
    }

    public void setBodyMap(Map<String, Object> bodyMap) {
        this.bodyMap = bodyMap;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public Map<String, Object> getBodyMap() {
        return bodyMap;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
}
