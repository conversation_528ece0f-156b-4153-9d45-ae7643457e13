package com.fls.system.model;

import cn.hutool.core.util.StrUtil;

import com.fls.framework.security.util.LoginHelper;
import com.fls.system.model.request.BaseBillRequest;
import com.fls.system.model.response.ResourceDetail;

import lombok.Data;

/**
 * 资源表信息
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
public class ResourceTableInfo {

    /**
     * 资源编码
     */
    private String resCode;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 资源对应表名
     */
    private String table;

    /**
     * 资源对应表主键列
     */
    private String idColumn;

    /**
     * 资源对应的表实例主键
     */
    private String idObj;

    /**
     * 单据状态，参考字典维护中的sheet_status
     */
    private String status;

    /**
     * 流程实例id
     */
    private String idProcessInstance;

    /**
     * 用户id
     */
    private String idUser;

    /**
     * 单独定义from方法，不影响其构造函数设计
     *
     * @param detail 资源详情
     * @param billRequest 请求参数
     * @param approveInfo 单据审批信息
     * @return 资源表详情
     */
    public static ResourceTableInfo from(ResourceDetail detail, BaseBillRequest billRequest, ResourceApproveInfo approveInfo) {
        ResourceTableInfo info = new ResourceTableInfo();
        info.setTable(detail.getTblName());
        info.setIdColumn(detail.getTblIdName());
        info.setResCode(billRequest.getResCode());
        info.setResName(detail.getName());
        info.setIdObj(billRequest.getIdObj());
        info.setStatus(approveInfo.getStatus());
        info.setIdProcessInstance(approveInfo.getIdProcessInstance());
        String currentUserId = StrUtil.isNotBlank(billRequest.getIdUser()) ? billRequest.getIdUser() : LoginHelper.getLoginUser().getIdUser();
        info.setIdUser(currentUserId);
        return info;
    }
}
