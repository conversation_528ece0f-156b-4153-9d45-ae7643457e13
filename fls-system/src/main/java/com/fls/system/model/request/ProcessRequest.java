package com.fls.system.model.request;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审批流程请求,此处将流程操作的字段都做合一处理，如果需要拆分职责，再进行细一步的拆分
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@NoArgsConstructor
public class ProcessRequest {

    /**
     * 操作人用户id
     */
    private String userId;

    /**
     * 单据编码
     */
    private String billCode;

    /**
     * 交易类型编码
     */
    private String transTypeCode;

    /**
     * 流程标题, *在*时间发起了*流程"
     */
    private String title;

    /**
     * 是否向上查找
     */
    private boolean findHead;

    /**
     * 审批结果，枚举：0：草稿，1：提交，2：已完成，3：已撤回，4：驳回，5：作废
     */
    private Integer result;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 处理意见
     */
    private String remarks;

    /**
     * 流程表单入参,JSON格式
     */
    private Object data;

    /**
     * 流程转办用户id
     */
    private String transUserId;

    public static ProcessRequest from(BaseBillRequest billRequest, String title, String userId, boolean findHead) {
        ProcessRequest request = new ProcessRequest();
        request.transTypeCode = billRequest.getTransCode();
        request.billCode = billRequest.getResCode();
        request.data = billRequest.getData();
        request.findHead = findHead;
        request.userId = userId;
        request.title = title;
        return request;
    }
}