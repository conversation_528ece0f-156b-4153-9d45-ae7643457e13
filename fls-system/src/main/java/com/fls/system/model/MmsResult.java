package com.fls.system.model;

import com.fls.framework.mybatis.model.PageResult;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * MMS系统返回结果
 *
 * <AUTHOR>
 * @date 2025/4/1 16:02
 */
public class MmsResult<T> implements Serializable {

    private String msg;

    private Integer code;

    private Boolean success;

    private MmsData data;

    class MmsData {

        private Integer total;

        private List<T> rows;

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public List<T> getRows() {
            return rows;
        }

        public void setRows(List<T> rows) {
            this.rows = rows;
        }
    }


    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public MmsData getData() {
        return data;
    }

    public void setData(MmsData data) {
        this.data = data;
    }

    public PageResult toPageResult(Integer pageNo, Integer pageSize) {
        PageResult result = new PageResult();
        result.setTotal(0);
        result.setSize(pageSize);
        result.setCurrent(pageNo);
        result.setRecords(Collections.emptyList());
        if (data != null && data.getTotal() != 0) {
            result.setTotal(data.getTotal());
            result.setRecords(data.getRows());
        }
        return result;
    }
}
