package com.fls.system.model.response;

import lombok.Data;

/**
 * 审批任务响应
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
public class ApproveTask {
    /**
     * 审计人id
     */
    private String auditor;
    /**
     * 审计人名称
     */
    private String auditorName;
    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 任务id
     */
    private String idTask;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 流程名称
     */
    private String procName;
    /**
     * 单据资源编码
     */
    private String resourceCode;
}