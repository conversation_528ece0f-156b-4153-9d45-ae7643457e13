package com.fls.system.model.request;

import javax.validation.constraints.NotBlank;

import com.fls.system.consts.ValidationGroups;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批流程请求
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApproveRequest extends BaseBillRequest {

    /**
     * 流程实例id
     */
    @NotBlank(groups = {ValidationGroups.Approve.class, ValidationGroups.Transfer.class}, message = "流程实例id不能为空")
    private String processInstanceId;

    /**
     * 处理意见
     */
    private String remarks;

    /**
     * 流程转办用户id
     */
    @NotBlank(groups = {ValidationGroups.Transfer.class}, message = "转办用户id不能为空")
    private String transUserId;
}