package com.fls.system.model;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资源审批信息
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@NoArgsConstructor
public class ResourceApproveInfo {

    /**
     * 资源表基础信息
     */
    private ResourceTableInfo resourceInfo;
    /**
     * 单据状态，参考字典维护中的sheet_status
     */
    private String status;
    /**
     * 审批时间
     */
    private LocalDateTime auditTime;
    /**
     * 审批人
     */
    private String auditor;
    /**
     * 流程实例id
     */
    private String idProcessInstance;

    /**
     * 资源实例id
     */
    private String idObj;

    /**
     * 构造函数
     *
     * @param resourceInfo 资源表信息
     */
    public ResourceApproveInfo(ResourceTableInfo resourceInfo) {
        this.resourceInfo = resourceInfo;
    }
}
