package com.fls.system.model.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cn.hutool.json.JSONObject;

import com.fls.system.consts.ValidationGroups;

import lombok.Data;

/**
 * 基础单据资源提交请求
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
public class BaseBillRequest {

    /**
     * 单据实例id
     */
    @NotBlank(groups = {ValidationGroups.Submit.class, ValidationGroups.Approve.class, ValidationGroups.Transfer.class, ValidationGroups.Retry.class}, message = "单据实例不能为空")
    private String idObj;

    /**
     * 单据资源id
     */
    @NotBlank(groups = {ValidationGroups.Submit.class, ValidationGroups.Approve.class, ValidationGroups.Transfer.class, ValidationGroups.Retry.class}, message = "单据资源编码不能为空")
    private String idRes;

    /**
     * 用户id
     */
    private String idUser;

    /**
     * 单据资源编码
     */
    private String resCode;

    /**
     * 单据资源交易类型id
     */
    private String idTransType;

    /**
     * 交易类型编码
     */
    private String transCode;

    /**
     * 流程表单入参,JSON格式
     */
    @NotNull(groups = {ValidationGroups.Submit.class}, message = "单据表单入参不能为空")
    private JSONObject data;
}