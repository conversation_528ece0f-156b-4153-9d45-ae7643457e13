package com.fls.system.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/27 9:51
 */
public class ComponentRouteConfig implements Serializable {

    // 路由路径
    private String path;

    // api-gatewag路径
    private String targetUrl;

    // 是否往 body 写入权限数据
    private boolean writePermissionData = false;

    // 请求方式（如 GET、POST 等）
    private String method = "POST";

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public boolean isWritePermissionData() {
        return writePermissionData;
    }

    public void setWritePermissionData(boolean writePermissionData) {
        this.writePermissionData = writePermissionData;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
}
