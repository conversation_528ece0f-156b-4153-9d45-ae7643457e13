package com.fls.system.model.response;

import lombok.Data;

/**
 * 审批流程历史响应
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
public class ApproveHistory {
    /**
     * 办理人
     */
    private String assignee;
    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 用时时间
     */
    private String duration;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 审批意见
     */
    private String message;
    /**
     * 执行环节
     */
    private String node;
    /**
     * 流程标题
     */
    private String title;
    /**
     * 办理状态
     */
    private String status;
}