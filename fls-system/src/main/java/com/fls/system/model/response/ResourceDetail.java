package com.fls.system.model.response;

import java.util.List;

import lombok.Data;

/**
 * 资源表详情信息
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
public class ResourceDetail {

    private String idResource;

    private String code;

    private String name;

    private String idResourceclass;

    private String classCode;

    private String className;

    private String moduleCode;

    private String needprocFlag;

    private String incalFlag;

    private String needauthFlag;

    private String incalLink;

    private String resourceType;

    private String needdocFlag;

    private String needapplyFlag;

    private String archmanaFlag;

    private String historyFlag;

    private String appFlag;

    private String appLink;

    private String domainFlag;

    private String quoteFlag;

    private String status;

    private String tblName;

    private String tblIdName;

    private List<ResTransType> transList;
}
