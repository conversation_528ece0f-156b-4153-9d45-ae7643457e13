# 数字化开发底座

#### 介绍
佛朗斯数字化开发底座

#### 核心依赖

| 依赖  | 版本     | 备注 |
|-----|--------|----|
| JDK | 8      |    |
| Spring Boot    | 2.7.2  |    |
|  Mybatis Plus   | 3.5.2  |    |
|  hutool   | 5.8.21 |    |
| Sa-Token | 1.38.0 | |
| Redisson | 3.18.0 | |
| dynamic-datasource | 4.1.3  | |



#### 模块说明

```lua
fls-dev-tookit
└── fls-compose -- 开发底座组合包
└── fls-framework -- 基础框架
	 ├──fls-framework-alibaba-bom --springcloud alibaba版本管理
	 ├──fls-framework-bom --fls framework版本管理
	 ├──fls-framework-core --fls framework核心模块
	 ├──fls-framework-mybatis --fls framework mybatis模块
	 ├──fls-framework-nacos --fls framework nacos模块
	 ├──fls-framework-redis --fls framework redis模块
	 ├──fls-framework-security --fls framework security模块
	 ├──fls-framework-web --fls framework web模块
└── fls-plugins -- 能力插件模块
├── fls-system -- 系统能力模块
├── fls-system-api -- 系统能力api模块
```
