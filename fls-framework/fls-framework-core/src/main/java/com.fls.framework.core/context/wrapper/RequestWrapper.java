package com.fls.framework.core.context.wrapper;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.Enumeration;

/**
 * <AUTHOR>
 */
@Getter
public class RequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body;

    /**
     * 这个必须加,复制request中的bufferedReader中的值
     * @param request
     * @throws IOException
     */
    public RequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        body = getBodyString(request);
    }

    /**
     * 获取请求Body
     *
     * @param request
     * @return
     */
    public byte[] getBodyString(final ServletRequest request) throws IOException {
        String contentType = request.getContentType();
        String bodyString ="";

        if (StrUtil.isNotBlank(contentType) && (contentType.contains("multipart/form-data") || contentType.contains("x-www-form-urlencoded"))){

            Enumeration<String> pars=request.getParameterNames();

            while(pars.hasMoreElements()){

                String n=pars.nextElement();

                bodyString+=n+"="+request.getParameter(n)+"&";

            }

            bodyString=bodyString.endsWith("&")?bodyString.substring(0, bodyString.length()-1):bodyString;

            return bodyString.getBytes(Charset.forName("UTF-8"));

        }else {
            //todo 原有实现使用了redission中的框架，不确定是否兼容，需要进一步验证 return StreamUtil.readBytes(request.getReader(), "UTF-8");
            return IoUtil.readBytes(request.getInputStream());
        }
    }


    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream bais = new ByteArrayInputStream(body);
        return new ServletInputStream() {

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener listener) {

            }

            @Override
            public int read() throws IOException {
                return bais.read();
            }
        };
    }

}
