package com.fls.framework.core.exception;

import com.fls.framework.core.exception.enums.abs.AbstractBaseExceptionEnum;
import lombok.Getter;

/**
 * 授权和鉴权异常
 * <AUTHOR>
 */
@Getter
public class PermissionException extends RuntimeException {

    private final Integer code;

    private final String errorMessage;

    public PermissionException(AbstractBaseExceptionEnum exception) {
        super(exception.getMessage());
        this.code = exception.getCode();
        this.errorMessage = exception.getMessage();
    }

}
