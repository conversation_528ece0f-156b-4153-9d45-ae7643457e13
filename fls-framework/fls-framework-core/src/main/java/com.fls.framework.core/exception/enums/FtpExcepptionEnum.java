package com.fls.framework.core.exception.enums;


import com.fls.framework.core.annotion.ExpEnumType;
import com.fls.framework.core.consts.ExpEnumConstant;
import com.fls.framework.core.exception.enums.abs.AbstractBaseExceptionEnum;
import com.fls.framework.core.factory.ExpEnumCodeFactory;

/**
 * <AUTHOR>
 */

@ExpEnumType(module = ExpEnumConstant.FLS_CORE_MODULE_EXP_CODE, kind = ExpEnumConstant.FTP_EXCEPTION_ENUM)
public enum FtpExcepptionEnum implements AbstractBaseExceptionEnum {

    /**
     * FTP连接建立失败
     */
    CONNECTION_ERROR(1, "FTP连接建立失败"),

    /**
     * FTP连接建立失败
     */
    UPLOAD_ERROR(2, "文件上传出现异常,请联系管理员"),

    DELETE_ERROR(3,"文件删除失败");

    private final Integer code;

    private final String message;

    FtpExcepptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }
}
