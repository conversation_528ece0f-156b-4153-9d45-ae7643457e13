package com.fls.framework.core.exception.enums;


import com.fls.framework.core.annotion.ExpEnumType;
import com.fls.framework.core.consts.ExpEnumConstant;
import com.fls.framework.core.exception.enums.abs.AbstractBaseExceptionEnum;
import com.fls.framework.core.factory.ExpEnumCodeFactory;

/**
 * 授权和鉴权异常的枚举
 * <AUTHOR>
 */
@ExpEnumType(module = ExpEnumConstant.FLS_CORE_MODULE_EXP_CODE, kind = ExpEnumConstant.PERMISSION_EXCEPTION_ENUM)
public enum PermissionExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 资源路径不存在
     */
    URL_NOT_EXIST(1, "资源路径不存在，请检查请求地址"),

    /**
     * 没有权限访问资源
     */
    NO_PERMISSION(2, "没有权限访问资源，请联系管理员"),

    /**
     * 没有权限操作该数据
     */
    NO_PERMISSION_OPERATE(3, "没有权限操作该数据，请联系管理员");

    private final Integer code;

    private final String message;

    PermissionExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
