package com.fls.framework.core.exception;

import com.fls.framework.core.exception.enums.abs.AbstractBaseExceptionEnum;
import lombok.Getter;

/**
 * 认证相关的异常
 * <AUTHOR>
 */
@Getter
public class AuthException extends RuntimeException {

    private final Integer code;

    private final String errorMessage;

    public AuthException(AbstractBaseExceptionEnum exception) {
        super(exception.getMessage());
        this.code = exception.getCode();
        this.errorMessage = exception.getMessage();
    }

}
