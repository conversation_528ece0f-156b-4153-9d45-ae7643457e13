package com.fls.framework.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * NC视图转换api配置
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Configuration
@ConfigurationProperties(prefix = "nc.view")
@Data
public class NCTransferViewApiConfig {

    private String saleOrderChangeUrl;

    private String allocationStockInBillUrl;

    private String foreignSaleOutBillUrl;

    private String foreignSaleOutItemUrl;

    private String foreignSaleDeliveryUrl;

    private String foreignSaleDeliveryItemUrl;
}
