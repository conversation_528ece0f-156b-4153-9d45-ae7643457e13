package com.fls.framework.core.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;

/**
 * <AUTHOR>
 */
@Configuration
public class FastJsonConfiguration implements WebMvcConfigurer {
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        /*添加fastjson*/
        converters.add(stringHttpMessageConverter());
        converters.add(fastJsonHttpMessageConverters(converters));

    }

    /**
     * 定义fastjson替代jackson，有些地方单独使用了fastjson所以无法使用fastjson做全局配置
     */
    private FastJsonHttpMessageConverter fastJsonHttpMessageConverters(List<HttpMessageConverter<?>> converters) {
        /*排除掉Jackson*/
        for (int i = converters.size() - 1; i >= 0; i--) {
            if (converters.get(i) instanceof MappingJackson2HttpMessageConverter) {
                converters.remove(i);
            }
        }
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        /*自定义fastjson配置*/
        FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(
            /*是否输出值为null的字段,默认为false,我们将它打开*/
            // SerializerFeature.WriteMapNullValue,
            /*将Collection类型字段的字段空值输出为[]*/
            SerializerFeature.WriteNullListAsEmpty,
            /*将字符串类型字段的空值输出为空字符串*/
            SerializerFeature.WriteNullStringAsEmpty,
            /*将数值类型字段的空值输出为0*/
            // SerializerFeature.WriteNullNumberAsZero,
            /*自定义日期格式*/
            SerializerFeature.WriteDateUseDateFormat,
            /*禁用循环引用*/
            SerializerFeature.DisableCircularReferenceDetect);
        fastJsonHttpMessageConverter.setFastJsonConfig(config);
        /*但是MappingJackson2HttpMessageConverter里面支持的MediaTypes为application/json*/
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastMediaTypes.add(MediaType.ALL);
        fastJsonHttpMessageConverter.setSupportedMediaTypes(fastMediaTypes);
        return fastJsonHttpMessageConverter;
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        return new StringHttpMessageConverter();
    }
}
