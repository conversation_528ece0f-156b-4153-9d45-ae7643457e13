package com.fls.framework.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 小程序校验相关配置
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Configuration
@Data
public class AppConfig {
    @Value("${app.minop.start-url}")
    private String appMinopStartUrl;
    @Value("${app.minop.secret}")
    private String appMinopSecret;
}
