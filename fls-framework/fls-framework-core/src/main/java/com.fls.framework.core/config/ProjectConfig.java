package com.fls.framework.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 小程序校验相关配置
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "project")
public class ProjectConfig {
    /**
     * 是否启用系统级别菜单
     */
    private Boolean systemApiEnable;
    /**
     * 项目应用编码
     */
    private String code;
    /**
     * 项目小程序应用编码
     */
    private String appletCode;
}
