package com.fls.framework.core.annotion;
import java.lang.annotation.*;

/**
 * 属性信息注解，仅仅可以用于域声明
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface PropertyMsg {
    /**
     * 提示语，用于标记哪个字段发生变更
     *
     * @return 提示语
     */
    String value() default "";

    /**
     * 变更属性名
     * @return 属性名
     */
    String propertyName();

}

