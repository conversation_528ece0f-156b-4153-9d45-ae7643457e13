package com.fls.framework.core.filter;

import cn.hutool.log.Log;
import com.fls.framework.core.context.requestno.RequestNoContext;
import com.fls.framework.core.context.wrapper.RequestWrapper;
import com.fls.framework.core.exception.enums.ServerExceptionEnum;
import com.fls.framework.core.util.ResponseUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * 这个过滤器，在所有请求之前，也在spring security filters之前
 *
 * <AUTHOR>
 */
@Component
@SuppressWarnings("ALL")
public class WebLogFilter extends OncePerRequestFilter {

	private static final Log log = Log.get();

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException {
		try {
			doFilter(request, response, filterChain);
		} catch (Exception e) {
			log.error("异常信息堆栈：{}",e);
			log.error(">>> 服务器运行异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
			ResponseUtil.responseExceptionError(response, ServerExceptionEnum.SERVER_ERROR.getCode(),
					ServerExceptionEnum.SERVER_ERROR.getMessage(), e.getStackTrace()[0].toString());
		}
	}

	private void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
		String url = request.getRequestURI();
		RequestWrapper requestWrapper = null;
		StringBuilder sb = new StringBuilder();
		if (request instanceof HttpServletRequest) {
			requestWrapper = new RequestWrapper(request);
			BufferedReader bufferedReader = requestWrapper.getReader();
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				sb.append(line);
			}
		}
		long time = System.currentTimeMillis();
		log.info("==============》请求号为：{} , url = {} , method = {} , params = {}  , requestBody = {} , request-no = {} ",RequestNoContext.get(), url, request.getMethod(),request.getQueryString(),sb.toString(), time);
		filterChain.doFilter(requestWrapper, response);
	}

}
