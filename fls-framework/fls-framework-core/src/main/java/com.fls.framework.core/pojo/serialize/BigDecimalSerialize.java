package com.fls.framework.core.pojo.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class BigDecimalSerialize extends JsonSerializer{

    @Override
    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if(o != null && o instanceof BigDecimal){
            BigDecimal bigDecomal = (BigDecimal) o;
            jsonGenerator.writeString(bigDecomal.setScale(2,BigDecimal.ROUND_DOWN)+"");
        }
    }
}

