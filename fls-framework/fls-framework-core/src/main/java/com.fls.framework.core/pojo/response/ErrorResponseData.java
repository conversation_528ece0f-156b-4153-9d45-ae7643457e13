package com.fls.framework.core.pojo.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 失败响应结果
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErrorResponseData extends ResponseData {

    /**
     * 异常的具体类名称
     */
//    private String exceptionClazz;

    ErrorResponseData() {
        super(false, DEFAULT_ERROR_CODE, DEFAULT_ERROR_MESSAGE, null);
    }

    ErrorResponseData(String msg) {
        super(false, DEFAULT_ERROR_CODE, msg, null);
    }

    ErrorResponseData(Object object) {
        super(false, DEFAULT_ERROR_CODE, "操作失败", object);
    }

    public ErrorResponseData(Integer code, String msg) {
        super(false, code, msg, null);
    }
    public ErrorResponseData(String msg, Object object) {
        super(false, DEFAULT_ERROR_CODE, msg, object);
    }

    ErrorResponseData(Integer code, String msg, Object object) {
        super(false, code, msg, object);
    }
}
