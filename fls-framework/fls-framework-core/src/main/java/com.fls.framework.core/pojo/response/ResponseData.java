package com.fls.framework.core.pojo.response;

import lombok.Data;

/**
 * 响应结果数据
 *
 * <AUTHOR>
 */
@Data
public class ResponseData {

    public static final String DEFAULT_SUCCESS_MESSAGE = "请求成功";

    public static final String DEFAULT_ERROR_MESSAGE = "网络异常";

    public static final Integer DEFAULT_SUCCESS_CODE = 200;

    public static final Integer DEFAULT_ERROR_CODE = 500;

    /**
     * 请求是否成功
     */
    private Boolean success;

    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应信息
     */
    private String msg;

    /**
     * 响应对象
     */
    private Object data;

    public ResponseData() {
    }

    public ResponseData(Boolean success, Integer code, String msg, Object data) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static SuccessResponseData success() {
        return new SuccessResponseData();
    }

    public static SuccessResponseData success(Object object) {
        return new SuccessResponseData(object);
    }

    public static SuccessResponseData success(Integer code, String msg, Object object) {
        return new SuccessResponseData(code, msg, object);
    }

    public static SuccessResponseData success(String msg, Object object) {
        return new SuccessResponseData(DEFAULT_SUCCESS_CODE, msg, object);
    }


    public static ErrorResponseData error() {
        return new ErrorResponseData();
    }

    public static ErrorResponseData error(Object object) {
        return new ErrorResponseData(object);
    }

    public static ErrorResponseData error(String msg) {
        return new ErrorResponseData(msg);
    }

    public static ErrorResponseData error(Integer code, String msg) {
        return new ErrorResponseData(code, msg);
    }

    public static ErrorResponseData error(String msg, Object object) {
        return new ErrorResponseData(msg, object);
    }

    public static ErrorResponseData error(Integer code, String msg, Object object) {
        return new ErrorResponseData(code, msg, object);
    }

    public static boolean isNotSuccess(ResponseData response) {
        return response.getCode() != 200;
    }
}
