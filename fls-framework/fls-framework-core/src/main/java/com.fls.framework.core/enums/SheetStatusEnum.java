package com.fls.framework.core.enums;

import lombok.Getter;

/**
 * 单据资源通用状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Getter
public enum SheetStatusEnum {

    /**
     * 新制单
     */
    SAVE("1", "新制单"),
    /**
     * 审核通过
     */
    AGREE("2", "审核通过"),
    /**
     * 重新提交
     */
    RESUBMIT("3", "重新提交"),
    /**
     * 审核不通过
     */
    DISAGREE("4", "审核不通过"),
    /**
     * 提交
     */
    SUBMIT("5", "新提交"),
    /**
     * 审批中
     */
    PROCESS("6", "审批中"),
    /**
     * 作废
     */
    VOIDED("9", "作废");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    SheetStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SheetStatusEnum of(String status) {
        for (SheetStatusEnum statusEnum : SheetStatusEnum.values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    public boolean isApproval() {
        return this == SUBMIT || this == PROCESS;
    }

    public boolean isNotApproval() {
        return this != SUBMIT && this != PROCESS;
    }

}
