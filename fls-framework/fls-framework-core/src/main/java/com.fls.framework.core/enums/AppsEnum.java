package com.fls.framework.core.enums;

import lombok.Getter;

/**
 * 应用枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AppsEnum {


    /**
     * IOA综合平台
     */
    APP_TEMPLATE("ioa","IOA综合平台"),
    /**
     * 小程序系统
     */
    APP_MINOP("miop","小程序客户端");


    /**
     * 系统编码
     */
    private final String code;

    /**
     * 系统名称
     */
    private final String name;

    AppsEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


}
