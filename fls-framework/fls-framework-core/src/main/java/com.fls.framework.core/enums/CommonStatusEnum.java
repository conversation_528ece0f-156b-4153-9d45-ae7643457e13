package com.fls.framework.core.enums;

import com.fls.framework.core.exception.ServiceException;
import com.fls.framework.core.exception.enums.StatusExceptionEnum;
import lombok.Getter;

/**
 * 公共状态
 *
 * <AUTHOR>
 */
@Getter
public enum CommonStatusEnum {

//    1=未启用，2=已启用，3=已停用

    NORMAL(1,"未启用"),

    /**
     * 正常
     */
    ENABLE(2,"已启用"),

    /**
     * 停用
     */
    DISABLE(3, "已停用");



    private final Integer code;

    private final String message;

    CommonStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 检查请求参数的状态是否正确
     *
     */
    public static void validateStatus(Integer code) {
        if (code == null) {
            throw new ServiceException(StatusExceptionEnum.REQUEST_EMPTY);
        }
        if (ENABLE.getCode().equals(code) || DISABLE.getCode().equals(code)) {
            return;
        }
        throw new ServiceException(StatusExceptionEnum.NOT_WRITE_STATUS);
    }

}
