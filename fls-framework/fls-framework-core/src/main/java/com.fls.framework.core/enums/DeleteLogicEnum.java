package com.fls.framework.core.enums;

import lombok.Getter;

/**
 * 逻辑删除枚举
 *
 * <AUTHOR>
 **/
@Getter
public enum DeleteLogicEnum {
    /**
     * 逻辑删除：否
     */
    DB_IS_NOT_DELETE("0","未删除"),


    /**
     * 逻辑删除：是
     */
    DB_IS_DELETE("1","已删除");




    private final String code;

    private final String name;

    DeleteLogicEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
