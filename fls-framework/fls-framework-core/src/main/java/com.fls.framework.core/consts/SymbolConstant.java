package com.fls.framework.core.consts;

/**
 * 符号常量
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
public interface SymbolConstant {

    String PERIOD = ".";

    String COMMA = ",";

    String COLON = ":";

    String SEMICOLON = ";";

    String EXCLAMATION_MARK = "!";

    String QUESTION_MARK = "?";

    String HYPHEN = "-";

    String ASTERISK = "*";

    String APOSTROPHE = "`";

    String DASH = "-";

    String UNDER_SCORE = "_";

    String SINGLE_QUOTATION_MARK = "'";

    String DOUBLE_QUOTATION_MARK = "\"";

    String LEFT_ROUND_BRACKETS = "(";

    String RIGHT_ROUND_BRACKETS = ")";

    String LEFT_SQUARE_BRACKETS = "[";

    String RIGHT_SQUARE_BRACKETS = "]";

    String LEFT_ANGLE_BRACKETS = "<";

    String RIGHT_ANGLE_BRACKETS = ">";

    String LEFT_CURLY_BRACKETS = "{";

    String RIGHT_CURLY_BRACKETS = "}";

    String DOLLAR = "$";

    String PERCENT = "%";

    String LEFT_DIVIDE = "/";

    String RIGHT_DIVIDE = "\\";

    String LEFT_DOUBLE_DIVIDE = "//";

    String RIGHT_DOUBLE_DIVIDE = "\\\\";

    String EQUAL = "=";
}
