package com.fls.framework.core.consts;

/**
 * 通用常量
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
public interface CommonConstant {
    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * id
     */
    String ID = "id";

    /**
     * 名称
     */
    String NAME = "name";

    /**
     * 编码
     */
    String CODE = "code";

    /**
     * 值
     */
    String VALUE = "value";

    /**
     * 默认标识状态的字段名称
     */
    String STATUS = "status";

    /**
     * 默认逻辑删除的状态值
     */
    String DEFAULT_LOGIC_DELETE_VALUE = "2";

    /**
     * 用户代理
     */
    String USER_AGENT = "User-Agent";

    /**
     * 请求头token表示
     */
    String AUTHORIZATION = "Authorization";

    /**
     * 数据权限标识
     */
    String DATA_SCOPE_KEY_HEADER = "url-component";

    /**
     * token名称
     */
    String TOKEN_NAME = "token";

    /**
     * token类型
     */
    String TOKEN_TYPE_BEARER = "Bearer";

    /**
     * 首页提示语
     */
    String INDEX_TIPS = "Welcome To fls";

    /**
     * 未知标识
     */
    String UNKNOWN = "Unknown";

    /**
     * 默认包名
     */
    String DEFAULT_PACKAGE_NAME = "com.fls";

    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "123456";

    /**
     * 请求号在header中的唯一标识
     */
    String REQUEST_NO_HEADER_NAME = "Request-No";

    /**
     * 数据库链接URL标识
     */
    String DATABASE_URL_NAME = "DATABASE_URL_NAME";

    /**
     * 数据库链接驱动标识
     */
    String DATABASE_DRIVER_NAME = "DATABASE_DRIVER_NAME";

    /**
     * 数据库用户标识
     */
    String DATABASE_USER_NAME = "DATABASE_USER_NAME";

    /**
     * 点选验证码
     */
    String IMAGE_CODE_TYPE = "clickWord";

    /**
     * undefined未知
     */
    String UNDEFINED = "undefined";

    /**
     * 状态：正常
     */
    String COMMON_STATUS_ENABLE = "0";

    /**
     * 状态：暂停
     */
    String COMMON_STATUS_DISABLED = "1";

    /**
     * 组织找不到的默认值
     */
    String DEFAULT_NOT_FOUND_ORG_ID = "0000";

    /**
     * 经营主体找不到的默认值
     */
    String DEFAULT_NOT_FOUND_UNIT_ID = "0000";

    /**
     * 用户找不到的默认值
     */
    String DEFAULT_NOT_FOUND_USER_ID = "0000";

    /**
     * 删除标志
     */
    String DELETE_FLAG_IS_DELETED = "1";
    String DELETE_FLAG_NOT_DELETED = "0";

    /**
     * 公用状态
     */
    String COMMON_STATUS_NORMAL = "2";

    /**
     * 默认用户id
     */
    String DEFAULT_USER_ID = "inner-server-call";
}
