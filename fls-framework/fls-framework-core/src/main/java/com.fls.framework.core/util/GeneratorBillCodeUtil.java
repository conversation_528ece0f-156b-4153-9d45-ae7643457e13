package com.fls.framework.core.util;

import java.util.HashSet;
import java.util.Set;

/**
 * @Author: ningfei
 * @Date: 2023/10/17 13:52
 * @Description:
 */
public class GeneratorBillCodeUtil {
    private static final String ALPHABET = "0123456789";
    private static final int BASE = ALPHABET.length();

    public static String generateNextKey(String initialValue, int length) {
        Set<String> usedKeys = new HashSet<>();

        // 将初始值补齐到指定位数
        StringBuilder paddedValue = new StringBuilder(initialValue);
        while (paddedValue.length() < length) {
            paddedValue.insert(0, '0');
        }
        String nextKey = paddedValue.toString();
        usedKeys.add(nextKey);

        // 生成下一个不重复的数字字母组合
        while (usedKeys.size() < Math.pow(BASE, length)) {
            char[] nextKeyChars = nextKey.toCharArray();
            for (int i = nextKeyChars.length - 1; i >= 0; i--) {
                int index = ALPHABET.indexOf(nextKeyChars[i]);
                if (index < BASE - 1) {
                    nextKeyChars[i] = ALPHABET.charAt(index + 1);
                    break;
                } else {
                    nextKeyChars[i] = ALPHABET.charAt(0);
                }
            }
            nextKey = new String(nextKeyChars);

            if (!usedKeys.contains(nextKey)) {
                usedKeys.add(nextKey);
                if (nextKey.length() == length) {
                    return nextKey;
                }
            }
        }

        return null;
    }
}