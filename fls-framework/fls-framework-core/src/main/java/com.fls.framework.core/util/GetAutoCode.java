package com.fls.framework.core.util;

import cn.hutool.core.text.CharSequenceUtil;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 生成流水号工具
 * <AUTHOR>
 */
public class GetAutoCode {
    /**
     * 三位流水号
     */
    private static final String STR_THREE_FORMAT = "000";
    /**
     * 四位流水号
     */
    private static final String STR_FOUR_FORMAT = "0000";
    /**
     * 日期格式
     */
    private static final String DATE_FORMAT = "yyyyMMdd";
    /**
     * 默认调用次数
     */
    private static final int DEFAULT_NUM = 1;
    /**
     * 默认前缀
     */
    private static final String DEFAULT_PREFIX = "";

    /**
     * @return 获取三位流水号
     */
    public static String getThreePipelineNumbers(int count) {
        String code = "";
        int num =  count + 1;
        DecimalFormat def = new DecimalFormat(STR_THREE_FORMAT);
        code += def.format(num);
        return code;
    }

    /**
     * 获取四位流水号
     * @param count 总数量
     * @return
     */
    public static String getFourPipelineNumbers(int count) {
        return getFourPipelineNumbers(DEFAULT_PREFIX,count);
    }

    /**
     * 获取四位流水号
     * @param prefix   流水号前缀
     * @param count  总数量
     * @return
     */
    public static String getFourPipelineNumbers(String prefix,int count) {
        StringBuffer code = new StringBuffer();
        if(CharSequenceUtil.isNotEmpty(prefix)){
            code.append(prefix);
        }
        int num =  count + 1;
        DecimalFormat def = new DecimalFormat(STR_FOUR_FORMAT);
        code.append(def.format(num));
        return code.toString();
    }

    /**
     * 四位流水号：日期 + 四位流水号  例：202208130001
     * @param maxNo 最大流水号
     * @param num 调用次数
     * @return
     */
    public static String getDateFourPipelineNumbers(String maxNo,int num) {
        return getDateFourPipelineNumbers(DEFAULT_PREFIX,maxNo,num);
    }

    /**
     * 四位流水号：日期 + 四位流水号  例：202208130001
     * @param maxNo 最大流水号
     * @return
     */
    public static String getDateFourPipelineNumbers(String maxNo) {
        return getDateFourPipelineNumbers(DEFAULT_PREFIX,maxNo,DEFAULT_NUM);
    }

    /**
     * 四位流水号：前缀  + 日期 + 四位流水号  例：CP202208130001
     * @param pfix 前缀
     * @param maxNo 最大流水号
     * @return
     */
    public static String getDateFourPipelineNumbers(String maxNo,String pfix) {
        return getDateFourPipelineNumbers(maxNo,pfix,DEFAULT_NUM);
    }

    /**
     * 四位流水号：前缀  + 日期 + 四位流水号  CP202208130001
     * @param pfix  前缀
     * @param maxNo 最大流水号
     * @param num  调用次数
     * @return
     */
    public static String getDateFourPipelineNumbers(String maxNo,String pfix, int num) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        String date = format.format(new Date());
        DecimalFormat df = new DecimalFormat(STR_FOUR_FORMAT);
        int temNum=num;
        //如果有传入流水号则进行截取拼接，否则直接生成一个
        if (CharSequenceUtil.isNotEmpty(maxNo)) {
            // 截取后四位
            String noEnd = maxNo.substring(maxNo.length() - 4);
            //截取日期部分
            String noCentre="";
            if(CharSequenceUtil.isNotEmpty(pfix)){
                noCentre=maxNo.substring(pfix.length(), pfix.length() + 8);
            }else{
                noCentre=maxNo.substring(0, 8);
            }
            //如果是同一天的增加序号，否则从1开始
            if (date.equals(noCentre)) {
                temNum += Integer.parseInt(noEnd);
            }
        }
        return pfix + date +  df.format(temNum);
    }
    public static void main(String[] args) {
        System.out.println(getDateFourPipelineNumbers("cp","cp2022081500011",10));
    }


}
