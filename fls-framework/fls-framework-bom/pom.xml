<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fls</groupId>
    <artifactId>fls-framework-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>framework 组件版本统一管理</description>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-nacos</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-redis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-web</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-excel</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-framework-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fls</groupId>
                <artifactId>fls-compose</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>fls-releases</id>
            <url>http://192.168.0.127:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>fls-snapshots</id>
            <url>http://192.168.0.127:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
