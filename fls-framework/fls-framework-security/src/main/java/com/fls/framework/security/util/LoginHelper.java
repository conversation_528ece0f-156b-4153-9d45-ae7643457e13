package com.fls.framework.security.util;

import java.util.Set;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;

import com.fls.framework.core.consts.CommonConstant;
import com.fls.framework.core.pojo.login.LoginUser;
import com.fls.framework.security.enums.DeviceType;

/**
 * <AUTHOR>
 */
public class LoginHelper {
    public static final String JOIN_CODE = ":";
    public static final String LOGIN_USER_KEY = "loginUser";

    /**
     * 登录系统
     *
     * @param loginUser 登录用户信息
     */
    public static void login(LoginUser loginUser) {
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        StpUtil.login(loginUser.getLoginId(), SaLoginConfig.setExtra("userId", loginUser.getIdUser()).setExtra("account", loginUser.getUsername()).setExtra("uuid", IdUtil.fastUUID()).setExtra("sub", loginUser.getIdUser())
            .setExtra("iat", loginUser.getLoginTime().getTime()).setExtra("exp", loginUser.getExpireTime().getTime()));
        setLoginUser(loginUser);
    }

    /**
     * 登录系统 基于 设备类型 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     */
    public static void loginByDevice(LoginUser loginUser, DeviceType deviceType) {
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        StpUtil.login(loginUser.getLoginId(), SaLoginConfig.setExtra("device", deviceType.getDevice()).setExtra("userId", loginUser.getIdUser()).setExtra("account", loginUser.getUsername()).setExtra("uuid", IdUtil.fastUUID())
            .setExtra("sub", loginUser.getIdUser()).setExtra("iat", loginUser.getLoginTime()).setExtra("exp", loginUser.getExpireTime()));
        setLoginUser(loginUser);
    }

    /**
     * 登录系统 基于 token 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     */
    public static void loginByToken(LoginUser loginUser, String token) {
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        StpUtil.login(loginUser.getLoginId(), SaLoginConfig.setToken(token));
        setLoginUser(loginUser);
    }

    /**
     * 获取用户(多级缓存)
     */
    public static LoginUser getLoginUser() {
        LoginUser loginUser = (LoginUser)SaHolder.getStorage().get(LOGIN_USER_KEY);
        if (loginUser != null) {
            return loginUser;
        }
        loginUser = (LoginUser)StpUtil.getTokenSession().get(LOGIN_USER_KEY);
        SaHolder.getStorage().set(LOGIN_USER_KEY, loginUser);
        return loginUser;
    }

    /**
     * 设置用户数据(多级缓存)
     */
    public static void setLoginUser(LoginUser loginUser) {
        StpUtil.getTokenSession().set(LOGIN_USER_KEY, loginUser);
        StpUtil.getSession().set(LOGIN_USER_KEY, loginUser);
    }

    public static String getUserIdOrDefault() {
        try {
            return getLoginUser().getIdUser();
        } catch (Exception e) {
            return CommonConstant.DEFAULT_USER_ID;
        }
    }

    /**
     * 更具token查询用户上下文信息
     * 
     * @param token 令牌
     * @return 用户信息
     */
    public static LoginUser getLoginUser(String token) {
        Object loginUser = StpUtil.getTokenSessionByToken(token).get(LOGIN_USER_KEY);
        if (ObjectUtil.isNotEmpty(loginUser)) {
            return (LoginUser)loginUser;
        }
        return null;
    }

    /**
     * 查询当前路径的数据权限
     * 
     * @return 数据权限
     */
    public static Set<String> getSysLoginUserCurrentOrgScopes() {
        LoginUser loginUser = getLoginUser();
        String url = SaHolder.getRequest().getHeader(CommonConstant.DATA_SCOPE_KEY_HEADER);
        if (ObjectUtil.isEmpty(url)) {
            url = SaHolder.getRequest().getRequestPath();
        }
        return loginUser.getOrgDataScopes().get(url);
    }

    /**
     * 查询当前路径的经营主体数据权限
     * 
     * @return 经营主体数据权限
     */
    public static Set<String> getSysLoginUserCurrentUnitScopes() {
        LoginUser loginUser = getLoginUser();
        String url = SaHolder.getRequest().getHeader(CommonConstant.DATA_SCOPE_KEY_HEADER);
        if (ObjectUtil.isEmpty(url)) {
            url = SaHolder.getRequest().getRequestPath();
        }
        return loginUser.getUnitDataScopes().get(url);
    }
}
