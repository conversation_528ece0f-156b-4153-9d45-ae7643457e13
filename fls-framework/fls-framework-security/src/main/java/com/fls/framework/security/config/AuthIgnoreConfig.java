package com.fls.framework.security.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "security.ignore")
public class AuthIgnoreConfig {
    private List<String> urls = new ArrayList<>();
}

