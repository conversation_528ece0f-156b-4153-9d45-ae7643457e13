package com.fls.framework.security.util.jwt;

import cn.dev33.satoken.SaManager;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fls.framework.core.context.requestno.RequestNoContext;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * JwtToken工具类
 *
 * <AUTHOR>
 * @date 2022/03/20
 */
@SuppressWarnings("ALL")
@Component
@Slf4j
public class JwtTokenUtil {


    /**
     * 生成token
     */
    public static String generateToken(JwtPayLoad jwtPayLoad) {

        DateTime expirationDate = DateUtil.offsetSecond(new Date(), 30000);
        String jwtToken = Jwts.builder()
                .setClaims(BeanUtil.beanToMap(jwtPayLoad))
                .setSubject(jwtPayLoad.getUserId())
                .setIssuedAt(new Date())
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, SaManager.getConfig().getJwtSecretKey().getBytes())
                .compact();
        return jwtToken;
    }

    /**
     * 根据token获取Claims
     *
     * @param token
     * @return
     */
    public static Claims getClaimsFromToken(String token) {
        String secretKey = SaManager.getConfig().getJwtSecretKey();
        return getClaimsFromToken(token, secretKey);
    }

    /**
     * 根据token获取Claims
     */
    public static Claims getClaimsFromToken(String token, String secret) {
        return Jwts.parser()
                .setSigningKey(secret.getBytes())
                .parseClaimsJws(token)
                .getBody();
    }


    public static void main(String[] args) {
        System.out.println(Jwts.parser().setSigningKey("fls-cloud@2022".getBytes())
                .parseClaimsJws("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjplMTg0NDFjYi1kMjg1LTQyYjctYWRhMy1mMWIzOGM3NjRlMjkiLCJyblN0ciI6IjZuV0xIQ2dWaGxFa29GQkNlM2N4R1FFdFpVMFhaNjV4In0.Jx_27ia-BiRatRLqlU7MVVgk1TQy2IYzRfHGXGanSc4").getBody());
    }


    /**
     * 获取JwtPayLoad部分
     */
    @SuppressWarnings("AliDeprecation")
    public static JwtPayLoad getJwtPayLoad(String token) {
        Claims claims = getClaimsFromToken(token);
        return BeanUtil.toBean(claims, JwtPayLoad.class);
    }

    public static JwtPayLoad getJwtPayLoad(String token, String secret) {
        Claims claims = getClaimsFromToken(token, secret);
        return BeanUtil.toBean(claims, JwtPayLoad.class);
    }

    /**
     * 校验token是否正确
     */
    public static Boolean checkToken(String token) {
        String secretKey = SaManager.getConfig().getJwtSecretKey();
        return checkToken(token, secretKey);
    }

    /**
     * 校验token是否正确
     */
    public static Boolean checkToken(String token, String secretKey) {
        try {
            getClaimsFromToken(token, secretKey);
            return true;
        } catch (JwtException e) {
            log.error(">>> Jwt异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
            return false;
        }
    }

    /**
     * 校验token是否失效
     */
    public static Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            final Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (ExpiredJwtException | NullPointerException e) {
            log.error(">>> Jwt异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
            return true;
        }
    }

    /**
     * 根据secret校验token是否失效
     */
    public static Boolean isTokenExpired(String token, String secret) {
        try {
            Claims claims = getClaimsFromToken(token, secret);
            final Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (ExpiredJwtException | NullPointerException e) {
            log.error(">>> Jwt异常，请求号为：{}，具体信息为：{}", RequestNoContext.get(), e.getMessage());
            return true;
        }
    }
}
