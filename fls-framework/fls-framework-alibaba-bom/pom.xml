<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fls</groupId>
    <artifactId>fls-framework-alibaba-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>fls framework alibaba版本依赖</description>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <spring-cloud-alibaba.version>2021.0.4.0</spring-cloud-alibaba.version>
        <nacos.client.version>2.0.4</nacos.client.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>fls-releases</id>
            <url>http://*************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>fls-snapshots</id>
            <url>http://*************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
