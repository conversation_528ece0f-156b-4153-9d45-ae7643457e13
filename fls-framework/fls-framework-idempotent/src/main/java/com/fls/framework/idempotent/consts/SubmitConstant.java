package com.fls.framework.idempotent.consts;

/**
 * 防重提交常量
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
public interface SubmitConstant {
    /**
     * 全局 redis key (业务无关的key)
     */
    String GLOBAL_REDIS_KEY = "global:";

    /**
     * 仿重提交默认间隔时间（毫秒）
     */
    int DEFAULT_INTERVAL = 5000;

    /**
     * 防重提交最小间隔时间（毫秒）
     */
    int MIN_INTERVAL = 1000;

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = GLOBAL_REDIS_KEY + "repeat_submit:";

    /**
     * 默认提示信息
     */
    String DEFAULT_MESSAGE = "请勿重复提交";
}
