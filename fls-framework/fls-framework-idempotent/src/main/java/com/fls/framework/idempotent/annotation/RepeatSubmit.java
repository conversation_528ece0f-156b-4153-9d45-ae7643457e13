package com.fls.framework.idempotent.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

import com.fls.framework.idempotent.consts.SubmitConstant;

/**
 * 防止表单重复提交注解
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RepeatSubmit {

    /**
     * 间隔时间，小于此时间视为重复提交，单位通过timeUnit定义
     */
    int interval() default SubmitConstant.DEFAULT_INTERVAL;

    /**
     * 时间单位，默认毫秒
     */
    TimeUnit timeUnit() default TimeUnit.MILLISECONDS;

    /**
     * 提示消息
     */
    String message() default SubmitConstant.DEFAULT_MESSAGE;

}
