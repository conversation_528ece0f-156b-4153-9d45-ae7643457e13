package com.fls.framework.idempotent.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConfiguration;

import com.fls.framework.idempotent.aspect.RepeatSubmitAspect;

/**
 * 幂等功能配置
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@AutoConfiguration(after = RedisConfiguration.class)
public class IdempotentConfig {

    @Bean
    public RepeatSubmitAspect repeatSubmitAspect() {
        return new RepeatSubmitAspect();
    }

}
