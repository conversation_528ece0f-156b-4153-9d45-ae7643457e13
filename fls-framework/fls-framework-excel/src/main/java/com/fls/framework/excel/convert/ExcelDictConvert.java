package com.fls.framework.excel.convert;

import java.lang.reflect.Field;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fls.framework.core.service.DictService;
import com.fls.framework.core.util.SpringUtils;
import com.fls.framework.core.util.StringUtils;
import com.fls.framework.excel.anotation.ExcelDictFormat;
import com.fls.framework.excel.utils.ExcelUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 字典格式化转换处理
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Slf4j
public class ExcelDictConvert implements Converter<Object> {

    @Override
    public Class<Object> supportJavaTypeKey() {
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        ExcelDictFormat anno = getAnnotation(contentProperty.getField());
        String type = anno.dictType();
        String value = cellData.getStringValue();
        String code;
        if (StringUtils.isBlank(type)) {
            code = ExcelUtil.reverseByExp(value, anno.readConverterExp());
        } else {
            // todo.导入的操作逻辑，暂不支持label转code
            code = SpringUtils.getBean(DictService.class).getDictValue(type, value);
        }
        return Convert.convert(contentProperty.getField().getType(), code);
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (ObjectUtil.isNull(object)) {
            return new WriteCellData<>("");
        }
        ExcelDictFormat anno = getAnnotation(contentProperty.getField());
        String type = anno.dictType();
        String code = Convert.toStr(object);
        String value;
        if (StringUtils.isBlank(type)) {
            value = ExcelUtil.convertByExp(code, anno.readConverterExp());
        } else {
            value = SpringUtils.getBean(DictService.class).getDictValue(type, code);
        }
        return new WriteCellData<>(value);
    }

    private ExcelDictFormat getAnnotation(Field field) {
        return AnnotationUtil.getAnnotation(field, ExcelDictFormat.class);
    }
}
