package com.fls.framework.mybatis.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;

import lombok.Data;

/**
 * 基础单据实体
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
public class BaseBillEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("id_org")
    private String idOrg;

    @TableField("id_bizunit")
    private String idBizunit;

    @TableField("id_resource")
    private String idResource;

    @TableField("id_res_trantype")
    private String idResTrantype;

    @TableField("bill_code")
    private String billCode;

    @TableField("bill_date")
    private LocalDate billDate;

    @TableField(value = "status")
    private String status;

    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "updater", fill = FieldFill.INSERT)
    private String updater;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("invalid_flag")
    private String invalidFlag;

    @TableField("invalider")
    private String invalider;

    @TableField("invalid_time")
    private LocalDateTime invalidTime;

    @TableLogic
    @TableField("delete_flag")
    private Integer deleteFlag;

    @TableField(value = "ts", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime ts;

    @TableField("id_source_bill")
    private String idSourceBill;

    @TableField("source_bill_code")
    private String sourceBillCode;

    @TableField("id_source_res")
    private String idSourceRes;

    @TableField("id_first_source_bill")
    private String idFirstSourceBill;

    @TableField("first_source_bill_code")
    private String firstSourceBillCode;

    @TableField("id_first_res")
    private String idFirstRes;

    @TableField("source_project_code")
    private String sourceProjectCode;
}
