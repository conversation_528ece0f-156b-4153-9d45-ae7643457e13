package com.fls.framework.mybatis.model;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: ningfei
 * @Date: 2023/8/25 14:29
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageInfo<T> implements Serializable {
    private List<T> pageData;
    private long page;
    private long pageSize;
    private long total;

    public PageInfo(Page page, List<T> records) {
        this.page = (int) page.getCurrent();
        this.pageSize = (int) page.getSize();
        this.total = (int) page.getTotal();
        this.pageData = records;
    }

    public PageInfo(IPage iPage) {
        this.page = (int) iPage.getCurrent();
        this.pageSize = (int) iPage.getSize();
        this.total = (int) iPage.getTotal();
        this.pageData = iPage.getRecords();
    }
}