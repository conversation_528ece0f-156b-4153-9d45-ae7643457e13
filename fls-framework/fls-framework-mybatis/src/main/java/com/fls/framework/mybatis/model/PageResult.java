package com.fls.framework.mybatis.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 页面结果
 * <AUTHOR>
 * @Date 2024/03/29/16:18
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class PageResult<T> {

    private int current;

    private int size;

    private int total;

    private List<T> records;

    public PageResult(Page page, List<T> records) {
        this.current = (int) page.getCurrent();
        this.size = (int) page.getSize();
        this.total = (int) page.getTotal();
        this.records = records;
    }
}
