package com.fls.framework.mybatis.identifier;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 自定义UUID生成器，用于自定义主键生成策略
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Component
public class CustomUuidGenerator implements IdentifierGenerator {

    /**
     * 雪花算法生成器
     */
    private final Snowflake snowflake = IdUtil.getSnowflake();

    @Override
    public boolean assignId(Object idValue) {
        // 默认逻辑，如果主键值为空，则需要生成新主键
        return idValue == null;
    }

    @Override
    public Number nextId(Object entity) {
        // 数字主键生成逻辑（适用于 ASSIGN_ID）
        return snowflake.nextId();
    }

    @Override
    public String nextUUID(Object entity) {
        // 生成带 "-" 的 UUID（适用于 ASSIGN_UUID）
        return UUID.randomUUID().toString();
    }
}
