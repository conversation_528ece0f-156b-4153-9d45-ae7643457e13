package com.fls.framework.mybatis.config;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.handlers.StrictFill;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.fls.framework.mybatis.identifier.CustomUuidGenerator;
import com.fls.framework.security.util.LoginHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * mybatis扩展插件配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan("com.fls.**.mapper")
public class MybatisConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(paginationInnerInterceptor());
        // 乐观锁插件
        interceptor.addInnerInterceptor(optimisticLockerInnerInterceptor());
        return interceptor;
    }

    /**
     * 分页插件，自动识别数据库类型
     */
    public PaginationInnerInterceptor paginationInnerInterceptor() {
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(-1L);
        // 分页合理化
        paginationInnerInterceptor.setOverflow(true);
        return paginationInnerInterceptor;
    }

    /**
     * 乐观锁插件
     */
    public OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor() {
        return new OptimisticLockerInnerInterceptor();
    }

    /**
     * 使用自定义UUID生成器
     */
    @Primary
    @Bean
    public IdentifierGenerator idGenerator() {
        return new CustomUuidGenerator();
    }

    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                String userId = LoginHelper.getUserIdOrDefault();
                this.strictInsertFill(metaObject, "creator", String.class, userId);
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now(ZoneId.systemDefault()));
                this.strictInsertFill(metaObject, "updater", String.class, userId);
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now(ZoneId.systemDefault()));
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                String userId = LoginHelper.getUserIdOrDefault();
                this.strictUpdateFill(metaObject, "updater", String.class, userId);
                this.setFieldValByName("updateTime", LocalDateTime.now(ZoneId.systemDefault()), metaObject);
            }

            /**
             * 严格模式填充策略，预留扩展逻辑
             * 
             * @param tableInfo 表信息
             * @param metaObject 元对象
             * @param strictFills 严格填充列表
             */
            private void updateFill(TableInfo tableInfo, MetaObject metaObject, List<StrictFill> strictFills) {
                if (tableInfo.isWithUpdateFill()) {
                    strictFills.forEach((i) -> {
                        String fieldName = i.getFieldName();
                        tableInfo.getFieldList().stream().filter((j) -> {
                            return j.getProperty().equals(fieldName) && i.getFieldType().equals(j.getPropertyType()) && j.isWithUpdateFill();
                        }).findFirst().ifPresent((j) -> {
                            this.setFieldValByName(fieldName, i.getFieldVal().get(), metaObject);
                        });
                    });
                }

            }
        };
    }
}
